<?php

/**
 * Ejemplo de uso con YouTube API
 * 
 * Este archivo muestra cómo usar el token de acceso de YouTube
 * para interactuar directamente con la API de YouTube Data v3.
 * 
 * NOTA: Los métodos como getChannelInfo(), getVideos(), etc. han sido
 * eliminados del YouTubeProvider. Este ejemplo muestra cómo usar
 * directamente la API de YouTube con el token obtenido.
 */

require_once __DIR__ . '/../../vendor/autoload.php';

use LBCDev\OAuthManager\Models\OAuthService;
use GuzzleHttp\Client;

// Obtener el servicio de YouTube
$youtubeService = OAuthService::where('service_type', 'youtube')
    ->where('is_active', true)
    ->first();

if (!$youtubeService) {
    echo "No se encontró ningún servicio de YouTube activo.\n";
    exit(1);
}

// Asegurar que tenemos un token válido
if (!$youtubeService->ensureValidToken()) {
    echo "No se pudo obtener un token válido para YouTube.\n";
    exit(1);
}

echo "Token válido obtenido para YouTube.\n";

try {
    // Crear cliente HTTP
    $client = new Client();
    
    // Headers comunes para todas las peticiones
    $headers = [
        'Authorization' => 'Bearer ' . $youtubeService->access_token,
        'Accept' => 'application/json',
    ];

    // === EJEMPLO 1: Obtener información del canal del usuario ===
    echo "\n=== INFORMACIÓN DEL CANAL ===\n";
    
    $response = $client->get('https://www.googleapis.com/youtube/v3/channels', [
        'headers' => $headers,
        'query' => [
            'part' => 'snippet,statistics,contentDetails',
            'mine' => 'true',
        ],
    ]);

    $channelData = json_decode($response->getBody()->getContents(), true);
    
    if (empty($channelData['items'])) {
        echo "No se encontró información del canal.\n";
    } else {
        $channel = $channelData['items'][0];
        $snippet = $channel['snippet'];
        $statistics = $channel['statistics'];
        
        echo "Título del canal: " . $snippet['title'] . "\n";
        echo "Descripción: " . substr($snippet['description'], 0, 100) . "...\n";
        echo "Fecha de creación: " . $snippet['publishedAt'] . "\n";
        echo "Suscriptores: " . number_format($statistics['subscriberCount']) . "\n";
        echo "Videos: " . number_format($statistics['videoCount']) . "\n";
        echo "Visualizaciones totales: " . number_format($statistics['viewCount']) . "\n";
        
        if (isset($snippet['thumbnails']['default']['url'])) {
            echo "Thumbnail: " . $snippet['thumbnails']['default']['url'] . "\n";
        }
        
        // Guardar el ID del canal para usar en otros ejemplos
        $channelId = $channel['id'];
        $uploadsPlaylistId = $channel['contentDetails']['relatedPlaylists']['uploads'];
    }

    // === EJEMPLO 2: Obtener videos del canal ===
    echo "\n=== VIDEOS DEL CANAL (últimos 10) ===\n";
    
    if (isset($uploadsPlaylistId)) {
        $response = $client->get('https://www.googleapis.com/youtube/v3/playlistItems', [
            'headers' => $headers,
            'query' => [
                'part' => 'snippet',
                'playlistId' => $uploadsPlaylistId,
                'maxResults' => 10,
            ],
        ]);

        $videosData = json_decode($response->getBody()->getContents(), true);
        
        if (empty($videosData['items'])) {
            echo "No se encontraron videos en el canal.\n";
        } else {
            foreach ($videosData['items'] as $index => $item) {
                $video = $item['snippet'];
                echo ($index + 1) . ". " . $video['title'] . "\n";
                echo "   ID: " . $video['resourceId']['videoId'] . "\n";
                echo "   Descripción: " . substr($video['description'], 0, 80) . "...\n";
                echo "   Publicado: " . $video['publishedAt'] . "\n";
                
                if (isset($video['thumbnails']['default']['url'])) {
                    echo "   Thumbnail: " . $video['thumbnails']['default']['url'] . "\n";
                }
                
                echo "   URL: https://www.youtube.com/watch?v=" . $video['resourceId']['videoId'] . "\n\n";
            }
        }
    }

    // === EJEMPLO 3: Buscar videos públicos ===
    echo "\n=== BÚSQUEDA DE VIDEOS PÚBLICOS ===\n";
    
    $searchQuery = 'Laravel tutorial';
    $response = $client->get('https://www.googleapis.com/youtube/v3/search', [
        'headers' => $headers,
        'query' => [
            'part' => 'snippet',
            'q' => $searchQuery,
            'type' => 'video',
            'maxResults' => 5,
        ],
    ]);

    $searchResults = json_decode($response->getBody()->getContents(), true);
    
    echo "Resultados de búsqueda para '{$searchQuery}':\n\n";
    
    if (empty($searchResults['items'])) {
        echo "No se encontraron resultados.\n";
    } else {
        foreach ($searchResults['items'] as $item) {
            $snippet = $item['snippet'];
            echo "- " . $snippet['title'] . "\n";
            echo "  Canal: " . $snippet['channelTitle'] . "\n";
            echo "  Publicado: " . $snippet['publishedAt'] . "\n";
            echo "  URL: https://www.youtube.com/watch?v=" . $item['id']['videoId'] . "\n\n";
        }
    }

    // === EJEMPLO 4: Obtener estadísticas de un video específico ===
    echo "\n=== ESTADÍSTICAS DE VIDEO ===\n";
    
    // Usar el primer video encontrado en la búsqueda
    if (!empty($searchResults['items'])) {
        $videoId = $searchResults['items'][0]['id']['videoId'];
        
        $response = $client->get('https://www.googleapis.com/youtube/v3/videos', [
            'headers' => $headers,
            'query' => [
                'part' => 'statistics,snippet',
                'id' => $videoId,
            ],
        ]);

        $videoData = json_decode($response->getBody()->getContents(), true);
        
        if (!empty($videoData['items'])) {
            $video = $videoData['items'][0];
            $stats = $video['statistics'];
            $snippet = $video['snippet'];
            
            echo "Video: " . $snippet['title'] . "\n";
            echo "Canal: " . $snippet['channelTitle'] . "\n";
            echo "Visualizaciones: " . number_format($stats['viewCount']) . "\n";
            echo "Likes: " . number_format($stats['likeCount'] ?? 0) . "\n";
            echo "Comentarios: " . number_format($stats['commentCount'] ?? 0) . "\n";
            echo "Duración: " . ($video['contentDetails']['duration'] ?? 'N/A') . "\n";
        }
    }

    // === EJEMPLO 5: Obtener comentarios de un video ===
    echo "\n=== COMENTARIOS DE VIDEO ===\n";
    
    if (!empty($searchResults['items'])) {
        $videoId = $searchResults['items'][0]['id']['videoId'];
        
        try {
            $response = $client->get('https://www.googleapis.com/youtube/v3/commentThreads', [
                'headers' => $headers,
                'query' => [
                    'part' => 'snippet',
                    'videoId' => $videoId,
                    'maxResults' => 5,
                    'order' => 'relevance',
                ],
            ]);

            $commentsData = json_decode($response->getBody()->getContents(), true);
            
            if (empty($commentsData['items'])) {
                echo "No se encontraron comentarios para este video.\n";
            } else {
                echo "Comentarios del video:\n\n";
                
                foreach ($commentsData['items'] as $item) {
                    $comment = $item['snippet']['topLevelComment']['snippet'];
                    echo "Autor: " . $comment['authorDisplayName'] . "\n";
                    echo "Fecha: " . $comment['publishedAt'] . "\n";
                    echo "Comentario: " . substr($comment['textDisplay'], 0, 100) . "...\n";
                    echo "Likes: " . $comment['likeCount'] . "\n\n";
                }
            }
        } catch (Exception $e) {
            echo "Los comentarios están deshabilitados para este video o no se pueden acceder.\n";
        }
    }

    // === EJEMPLO 6: Obtener playlists del canal ===
    echo "\n=== PLAYLISTS DEL CANAL ===\n";
    
    if (isset($channelId)) {
        $response = $client->get('https://www.googleapis.com/youtube/v3/playlists', [
            'headers' => $headers,
            'query' => [
                'part' => 'snippet,contentDetails',
                'channelId' => $channelId,
                'maxResults' => 10,
            ],
        ]);

        $playlistsData = json_decode($response->getBody()->getContents(), true);
        
        if (empty($playlistsData['items'])) {
            echo "No se encontraron playlists en el canal.\n";
        } else {
            echo "Playlists encontradas: " . count($playlistsData['items']) . "\n\n";
            
            foreach ($playlistsData['items'] as $playlist) {
                $snippet = $playlist['snippet'];
                $contentDetails = $playlist['contentDetails'];
                
                echo "📋 " . $snippet['title'] . "\n";
                echo "   Descripción: " . substr($snippet['description'], 0, 80) . "...\n";
                echo "   Videos: " . $contentDetails['itemCount'] . "\n";
                echo "   Creada: " . $snippet['publishedAt'] . "\n";
                echo "   URL: https://www.youtube.com/playlist?list=" . $playlist['id'] . "\n\n";
            }
        }
    }

} catch (Exception $e) {
    echo "Error al usar la API de YouTube: " . $e->getMessage() . "\n";
    
    // Si es un error HTTP, mostrar más detalles
    if (method_exists($e, 'getResponse') && $e->getResponse()) {
        $response = $e->getResponse();
        echo "Código de estado: " . $response->getStatusCode() . "\n";
        echo "Respuesta: " . $response->getBody()->getContents() . "\n";
    }
}

echo "\n=== EJEMPLO COMPLETADO ===\n";
echo "Para más información sobre la API de YouTube Data v3, visita:\n";
echo "https://developers.google.com/youtube/v3/docs\n";
