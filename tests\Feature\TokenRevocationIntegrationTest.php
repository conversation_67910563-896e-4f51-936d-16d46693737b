<?php

namespace LBCDev\OAuthManager\Tests\Feature;

use LBCDev\OAuthManager\Tests\TestCase;
use LBCDev\OAuthManager\Models\OAuthService;
use LBCDev\OAuthManager\Services\OAuthManager;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Mockery;

class TokenRevocationIntegrationTest extends TestCase
{
    use RefreshDatabase;

    public function test_complete_token_revocation_workflow()
    {
        // Crear un servicio con tokens válidos
        $service = OAuthService::factory()->create([
            'name' => 'Test Google Drive',
            'service_type' => 'google_drive',
            'slug' => 'test-google-drive',
            'credentials' => [
                'client_id' => 'test_client_id',
                'client_secret' => 'test_client_secret',
            ],
            'access_token' => 'valid_access_token',
            'refresh_token' => 'valid_refresh_token',
            'expires_at' => now()->addHour(),
            'last_used_at' => now()->subMinutes(30),
            'is_active' => true,
        ]);

        // Verificar estado inicial
        $this->assertNotNull($service->access_token);
        $this->assertNotNull($service->refresh_token);
        $this->assertNotNull($service->expires_at);
        $this->assertNotNull($service->last_used_at);
        $this->assertFalse($service->isTokenExpired());

        // Mock del provider para simular revocación exitosa
        $mock = Mockery::mock(\LBCDev\OAuthManager\Providers\GoogleDriveProvider::class);
        $mock->shouldReceive('revokeToken')->once()->andReturn(true);

        $service->setProvider($mock);

        // Revocar token usando el modelo directamente
        $result = $service->revokeToken();

        // Verificar resultado
        $this->assertTrue($result);

        // Verificar que los tokens fueron limpiados
        $service->refresh();
        $this->assertNull($service->access_token);
        $this->assertNull($service->refresh_token);
        $this->assertNull($service->expires_at);
        $this->assertNull($service->last_used_at);

        // Verificar que otros datos se preservaron
        $this->assertEquals('Test Google Drive', $service->name);
        $this->assertEquals('google_drive', $service->service_type);
        $this->assertEquals('test-google-drive', $service->slug);
        $this->assertEquals([
            'client_id' => 'test_client_id',
            'client_secret' => 'test_client_secret',
        ], $service->credentials);
        $this->assertTrue($service->is_active);
    }

    public function test_oauth_manager_revocation_workflow()
    {
        $service1 = OAuthService::factory()->create([
            'name' => 'Google Drive 1',
            'service_type' => 'google_drive',
            'access_token' => 'token1',
            'is_active' => true,
        ]);

        $service2 = OAuthService::factory()->create([
            'name' => 'Google Drive 2',
            'service_type' => 'google_drive',
            'access_token' => 'token2',
            'is_active' => true,
        ]);

        // $manager = new OAuthManager();

        // // Verificar que el manager puede encontrar el servicio correcto
        // $foundService = $manager->getService('google_drive', 'Google Drive 1');
        // $this->assertNotNull($foundService);
        // $this->assertEquals('Google Drive 1', $foundService->name);

        // Mock para el primer servicio y revocar directamente
        $mock1 = Mockery::mock(\LBCDev\OAuthManager\Providers\GoogleDriveProvider::class);
        $mock1->shouldReceive('revokeToken')->once()->andReturn(true);
        $service1->setProvider($mock1);

        $result = $service1->revokeToken();

        $this->assertTrue($result);

        // Verificar que solo el servicio correcto fue afectado
        $service1->refresh();
        $service2->refresh();

        $this->assertNull($service1->access_token);
        $this->assertNotNull($service2->access_token);
    }

    public function test_revocation_with_provider_failure_still_clears_local_tokens()
    {
        $service = OAuthService::factory()->create([
            'name' => 'Test Service',
            'service_type' => 'google_drive',
            'access_token' => 'valid_token',
            'refresh_token' => 'valid_refresh',
            'expires_at' => now()->addHour(),
        ]);

        // Mock que simula fallo en la revocación remota
        $mock = Mockery::mock(\LBCDev\OAuthManager\Providers\GoogleDriveProvider::class);
        $mock->shouldReceive('revokeToken')->once()->andReturn(false);

        $service->setProvider($mock);

        $result = $service->revokeToken();

        // El resultado debe ser false porque la revocación remota falló
        $this->assertFalse($result);

        // Pero los tokens locales deben estar limpiados
        $service->refresh();
        $this->assertNull($service->access_token);
        $this->assertNull($service->refresh_token);
        $this->assertNull($service->expires_at);
    }

    public function test_revocation_with_provider_exception_still_clears_local_tokens()
    {
        $service = OAuthService::factory()->create([
            'name' => 'Test Service',
            'service_type' => 'google_drive',
            'access_token' => 'valid_token',
            'refresh_token' => 'valid_refresh',
        ]);

        // Mock que simula excepción en el provider
        $mock = Mockery::mock(\LBCDev\OAuthManager\Providers\GoogleDriveProvider::class);
        $mock->shouldReceive('revokeToken')->once()->andThrow(new \Exception('Network error'));

        $service->setProvider($mock);

        $result = $service->revokeToken();

        // El resultado debe ser false debido a la excepción
        $this->assertFalse($result);

        // Pero los tokens locales deben estar limpiados
        $service->refresh();
        $this->assertNull($service->access_token);
        $this->assertNull($service->refresh_token);
    }

    public function test_multiple_services_revocation_workflow()
    {
        $googleService = OAuthService::factory()->create([
            'name' => 'Google Drive',
            'service_type' => 'google_drive',
            'access_token' => 'google_token',
            'is_active' => true,
        ]);

        $onedriveService = OAuthService::factory()->create([
            'name' => 'OneDrive',
            'service_type' => 'onedrive',
            'access_token' => 'onedrive_token',
            'is_active' => true,
        ]);

        // Mocks para ambos servicios
        $googleMock = Mockery::mock(\LBCDev\OAuthManager\Providers\GoogleDriveProvider::class);
        $googleMock->shouldReceive('revokeToken')->once()->andReturn(true);

        $onedriveMock = Mockery::mock(\LBCDev\OAuthManager\Providers\OneDriveProvider::class);
        $onedriveMock->shouldReceive('revokeToken')->once()->andReturn(true);

        $googleService->setProvider($googleMock);
        $onedriveService->setProvider($onedriveMock);

        // Revocar ambos tokens directamente
        $googleResult = $googleService->revokeToken();
        $onedriveResult = $onedriveService->revokeToken();

        $this->assertTrue($googleResult);
        $this->assertTrue($onedriveResult);

        // Verificar que ambos servicios fueron limpiados
        $googleService->refresh();
        $onedriveService->refresh();

        $this->assertNull($googleService->access_token);
        $this->assertNull($onedriveService->access_token);
    }

    protected function tearDown(): void
    {
        parent::tearDown();
        Mockery::close();
    }
}
