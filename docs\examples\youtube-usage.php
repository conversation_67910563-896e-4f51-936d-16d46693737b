<?php

/**
 * Ejemplo de uso del YouTubeProvider
 * 
 * Este archivo muestra cómo usar el YouTubeProvider para:
 * - Crear un servicio OAuth de YouTube
 * - Autorizar el servicio
 * - Obtener tokens válidos
 * - Usar la API de YouTube
 */

require_once __DIR__ . '/../vendor/autoload.php';

use LBCDev\OAuthManager\Models\OAuthService;

// 1. Crear un servicio OAuth de YouTube
$youtubeService = OAuthService::create([
    'name' => 'Mi canal de YouTube',
    'service_type' => 'youtube',
    'slug' => 'mi-youtube',
    'credentials' => [
        'client_id' => 'tu-client-id',
        'client_secret' => 'tu-client-secret',
    ],
    'is_active' => true,
]);

echo "Servicio de YouTube creado con ID: " . $youtubeService->id . "\n";

// 2. Obtener URL de autorización
$provider = $youtubeService->getProviderInstance();
$authUrl = $provider->getAuthorizationUrl();

echo "URL de autorización: " . $authUrl . "\n";
echo "Visita esta URL para autorizar la aplicación.\n";

// 3. Simular que ya tenemos un token (en la práctica, esto vendría del callback)
$youtubeService->update([
    'access_token' => 'token_de_ejemplo',
    'refresh_token' => 'refresh_token_de_ejemplo',
    'expires_at' => now()->addHours(1),
]);

// 4. Obtener token válido usando el modelo directamente
if ($youtubeService->ensureValidToken()) {
    echo "Token válido obtenido: " . substr($youtubeService->access_token, 0, 20) . "...\n";

    // 5. Usar el provider directamente
    try {
        // Solo métodos básicos disponibles: getAuthorizationUrl, handleCallback, refreshToken, revokeToken
        echo "Provider configurado correctamente.\n";
        echo "Para operaciones avanzadas con YouTube, usa la API directamente.\n";
        echo "Ver ejemplo completo en: docs/examples/youtube-usage-api.php\n";
    } catch (Exception $e) {
        echo "Error al usar la API de YouTube: " . $e->getMessage() . "\n";
    }
} else {
    echo "No se pudo obtener un token válido.\n";
}

// 9. Ejemplo de renovación de token
echo "\n=== RENOVACIÓN DE TOKEN ===\n";
try {
    $refreshResult = $provider->refreshToken();
    if ($refreshResult) {
        echo "Token renovado exitosamente.\n";
        echo "Nuevo access_token: " . substr($refreshResult['access_token'], 0, 20) . "...\n";
    } else {
        echo "No se pudo renovar el token.\n";
    }
} catch (Exception $e) {
    echo "Error al renovar token: " . $e->getMessage() . "\n";
}

// 10. Ejemplo de revocación de token
echo "\n=== REVOCACIÓN DE TOKEN ===\n";
$revokeConfirm = readline("¿Deseas revocar el token? (y/N): ");
if (strtolower($revokeConfirm) === 'y') {
    try {
        $revokeResult = $provider->revokeToken();
        echo "Token revocado: " . ($revokeResult ? 'ÉXITO' : 'FALLO') . "\n";

        // Limpiar tokens localmente
        $youtubeService->update([
            'access_token' => null,
            'refresh_token' => null,
            'expires_at' => null,
        ]);

        echo "Tokens locales limpiados.\n";
    } catch (Exception $e) {
        echo "Error al revocar token: " . $e->getMessage() . "\n";
    }
} else {
    echo "Token no revocado.\n";
}

echo "\n=== EJEMPLO COMPLETADO ===\n";
echo "Para usar este ejemplo en tu aplicación:\n";
echo "1. Configura las credenciales de YouTube en tu .env\n";
echo "2. Crea el servicio OAuth usando el modelo OAuthService\n";
echo "3. Redirige al usuario a la URL de autorización\n";
echo "4. Maneja el callback para obtener los tokens\n";
echo "5. Usa los métodos del provider para interactuar con YouTube\n";

/**
 * Ejemplo de integración en un controlador Laravel:
 *
 * class YouTubeController extends Controller
 * {
 *     public function connect()
 *     {
 *         $service = OAuthService::create([
 *             'name' => 'YouTube Channel',
 *             'service_type' => 'youtube',
 *             'slug' => 'youtube-' . auth()->id(),
 *             'credentials' => [
 *                 'client_id' => config('oauth-manager.services.youtube.fields.client_id'),
 *                 'client_secret' => config('oauth-manager.services.youtube.fields.client_secret'),
 *             ],
 *             'is_active' => true,
 *         ]);
 *
 *         return redirect()->route('oauth-manager.authorize', ['service' => $service]);
 *     }
 *
 *     public function dashboard()
 *     {
 *         $service = OAuthService::where('service_type', 'youtube')
 *                                ->where('is_active', true)
 *                                ->first();
 *
 *         if (!$service || !$service->ensureValidToken()) {
 *             return redirect()->route('youtube.connect');
 *         }
 *
 *         $provider = $service->getProviderInstance();
 *         $channelInfo = $provider->getChannelInfo();
 *         $videos = $provider->getVideos(20);
 *
 *         return view('youtube.dashboard', compact('channelInfo', 'videos'));
 *     }
 * }
 */
