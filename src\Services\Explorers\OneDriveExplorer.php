<?php

namespace LBCDev\OAuthFileExplorer\Services\Explorers;

use LBCDev\OAuthFileExplorer\Services\AbstractFileExplorer;
use GuzzleHttp\Client;
use GuzzleHttp\Exception\RequestException;

class OneDriveExplorer extends AbstractFileExplorer
{
    protected $httpClient;
    protected $baseUrl = 'https://graph.microsoft.com/v1.0';
    protected $isConsumerAccount = false;

    /**
     * Initialize Microsoft Graph service with OAuth token
     */
    protected function initializeService(): void
    {
        $this->ensureValidToken();

        // Detect if this is a consumer (personal) account
        // $this->detectAccountType();

        $this->httpClient = new Client([
            'base_uri' => $this->baseUrl,
            'headers' => [
                'Authorization' => 'Bearer ' . $this->oauthService->access_token,
                'Content-Type' => 'application/json',
                'Accept' => 'application/json'
            ],
            'timeout' => 30
        ]);
    }

    /**
     * Detect if this is a consumer account by checking token claims
     */
    protected function detectAccountType(): void
    {
        try {
            // Try to decode the JWT token to check account type
            $tokenParts = explode('.', $this->oauthService->access_token);
            if (count($tokenParts) === 3) {
                $payload = json_decode(base64_decode($tokenParts[1]), true);

                // Consumer accounts typically have 'tid' that equals '9188040d-6c67-4c5b-b112-36a304b66dad'
                // or 'idtyp' set to 'user' with specific characteristics
                if (isset($payload['tid']) && $payload['tid'] === '9188040d-6c67-4c5b-b112-36a304b66dad') {
                    $this->isConsumerAccount = true;
                }

                // Alternative check: consumer accounts don't have 'roles' claim typically
                if (!isset($payload['roles']) && isset($payload['scp']) && strpos($payload['scp'], 'Files.ReadWrite') !== false) {
                    $this->isConsumerAccount = true;
                }
            }
        } catch (\Exception $e) {
            // If we can't detect, assume organizational account
            $this->isConsumerAccount = false;
        }
    }

    public function getServiceType(): string
    {
        return 'onedrive';
    }

    protected function getFolderMimeType(): string
    {
        return 'folder';
    }

    /**
     * List files and folders in a specific folder
     */
    public function listFiles(string $folderId = 'root', int $pageSize = 50): array
    {
        $this->ensureValidToken();

        if (!$this->httpClient) {
            $this->initializeService();
        }

        try {
            $endpoint = $folderId === 'root'
                ? '/me/drive/root/children'
                : "/me/drive/items/{$folderId}/children";

            $endpoint = $this->baseUrl . $endpoint;

            $response = $this->httpClient->get($endpoint, [
                'query' => [
                    '$top' => $pageSize,
                    '$select' => 'id,name,size,lastModifiedDateTime,folder,file,webUrl,downloadUrl,parentReference',
                    '$orderby' => 'name asc'
                ]
            ]);

            $data = json_decode($response->getBody()->getContents(), true);

            $formattedFiles = [];
            if (isset($data['value']) && is_array($data['value'])) {
                foreach ($data['value'] as $item) {
                    $formattedFiles[] = $this->formatFileData($item);
                }
            }

            return [
                'files' => $formattedFiles,
                'nextPageToken' => $data['@odata.nextLink'] ?? null
            ];
        } catch (RequestException $e) {
            // If /me fails, it might be a consumer account issue - try alternative approach
            if ($this->shouldRetryWithAlternativeEndpoint($e)) {
                return $this->listFilesWithAlternativeEndpoint($folderId, $pageSize);
            }

            $this->handleApiError($e);
            throw new \Exception('Error listing files: ' . $e->getMessage());
        }
    }

    /**
     * Get file information by ID
     */
    public function getFile(string $fileId): array
    {
        $this->ensureValidToken();

        if (!$this->httpClient) {
            $this->initializeService();
        }

        try {
            $endpoint = $fileId === 'root'
                ? 'me/drive/root'
                : "me/drive/items/{$fileId}";

            $response = $this->httpClient->get($endpoint, [
                'query' => [
                    '$select' => 'id,name,size,lastModifiedDateTime,folder,file,webUrl,@microsoft.graph.downloadUrl,parentReference'
                ]
            ]);

            $data = json_decode($response->getBody()->getContents(), true);
            return $this->formatFileData($data);
        } catch (RequestException $e) {
            $this->handleApiError($e);
            throw new \Exception('Error getting file: ' . $e->getMessage());
        }
    }

    /**
     * Get folder breadcrumb path
     */
    public function getBreadcrumb(string $folderId): array
    {
        if ($folderId === 'root') {
            return [['id' => 'root', 'name' => 'OneDrive']];
        }

        if (!$this->httpClient) {
            $this->initializeService();
        }

        try {
            $breadcrumb = [];
            $currentId = $folderId;

            // Get current folder info and build breadcrumb by traversing up
            while ($currentId !== 'root' && $currentId !== null) {
                $response = $this->httpClient->get("me/drive/items/{$currentId}", [
                    'query' => [
                        '$select' => 'id,name,parentReference'
                    ]
                ]);

                $data = json_decode($response->getBody()->getContents(), true);

                array_unshift($breadcrumb, [
                    'id' => $data['id'],
                    'name' => $data['name']
                ]);

                // Move to parent
                $currentId = isset($data['parentReference']['id'])
                    ? $data['parentReference']['id']
                    : 'root';
            }

            // Add root at the beginning
            array_unshift($breadcrumb, ['id' => 'root', 'name' => 'OneDrive']);

            return $breadcrumb;
        } catch (RequestException $e) {
            // Return root if error getting breadcrumb
            return [['id' => 'root', 'name' => 'OneDrive']];
        }
    }

    /**
     * Search files by name
     */
    public function searchFiles(string $query, string $folderId = 'root'): array
    {
        $this->ensureValidToken();

        if (!$this->httpClient) {
            $this->initializeService();
        }

        try {
            // Use Microsoft Graph search endpoint
            $endpoint = $folderId === 'root'
                ? "me/drive/root/search(q='{$query}')"
                : "me/drive/items/{$folderId}/search(q='{$query}')";

            $response = $this->httpClient->get($endpoint, [
                'query' => [
                    '$top' => 50,
                    '$select' => 'id,name,size,lastModifiedDateTime,folder,file,webUrl,@microsoft.graph.downloadUrl,parentReference',
                    '$orderby' => 'folder desc,name asc'
                ]
            ]);

            $data = json_decode($response->getBody()->getContents(), true);

            $formattedFiles = [];
            if (isset($data['value']) && is_array($data['value'])) {
                foreach ($data['value'] as $item) {
                    $formattedFiles[] = $this->formatFileData($item);
                }
            }

            return $formattedFiles;
        } catch (RequestException $e) {
            $this->handleApiError($e);
            throw new \Exception('Error searching files: ' . $e->getMessage());
        }
    }

    /**
     * Perform connection test
     */
    protected function performConnectionTest(): bool
    {
        try {
            if (!$this->httpClient) {
                $this->initializeService();
            }

            $response = $this->httpClient->get('me/drive', [
                'query' => ['$select' => 'id,driveType']
            ]);

            return $response->getStatusCode() === 200;
        } catch (\Exception $e) {
            return false;
        }
    }

    /**
     * Format file data to standard structure
     */
    protected function formatFileData($file): array
    {
        if (!is_array($file)) {
            throw new \InvalidArgumentException('Expected array data from OneDrive API');
        }

        $isFolder = isset($file['folder']);
        $mimeType = $isFolder ? 'folder' : ($file['file']['mimeType'] ?? 'application/octet-stream');

        // Generate icon link based on file type
        $iconLink = $this->getFileIconUrl($mimeType, $isFolder);

        return $this->getStandardFileStructure(
            $file['id'],
            $file['name'],
            $mimeType,
            $isFolder,
            [
                'size' => isset($file['size']) ? (int) $file['size'] : 0,
                'sizeFormatted' => isset($file['size']) ? $this->formatFileSize((int) $file['size']) : '-',
                'modifiedTime' => $file['lastModifiedDateTime'] ?? null,
                'webViewLink' => $file['webUrl'] ?? null,
                'webContentLink' => $file['@microsoft.graph.downloadUrl'] ?? null,
                'iconLink' => $iconLink,
                'parents' => isset($file['parentReference']['id']) ? [$file['parentReference']['id']] : [],
                'downloadUrl' => $isFolder ? null : ($file['@microsoft.graph.downloadUrl'] ?? null),
                'driveId' => $file['parentReference']['driveId'] ?? null,
                'path' => $file['parentReference']['path'] ?? null
            ]
        );
    }

    /**
     * Alternative method for consumer accounts that might have issues with /me endpoint
     */
    protected function listFilesWithAlternativeEndpoint(string $folderId = 'root', int $pageSize = 50): array
    {
        try {
            // First get user info to construct proper drive path
            $userResponse = $this->httpClient->get('me', [
                'query' => ['$select' => 'id']
            ]);
            $userData = json_decode($userResponse->getBody()->getContents(), true);
            $userId = $userData['id'];

            // Use users/{id}/drive endpoint instead of me/drive
            $endpoint = $folderId === 'root'
                ? "users/{$userId}/drive/root/children"
                : "users/{$userId}/drive/items/{$folderId}/children";

            $response = $this->httpClient->get($endpoint, [
                'query' => [
                    '$top' => $pageSize,
                    '$select' => 'id,name,size,lastModifiedDateTime,folder,file,webUrl,@microsoft.graph.downloadUrl,parentReference',
                    '$orderby' => 'folder desc,name asc'
                ]
            ]);

            $data = json_decode($response->getBody()->getContents(), true);

            $formattedFiles = [];
            if (isset($data['value']) && is_array($data['value'])) {
                foreach ($data['value'] as $item) {
                    $formattedFiles[] = $this->formatFileData($item);
                }
            }

            return [
                'files' => $formattedFiles,
                'nextPageToken' => $data['@odata.nextLink'] ?? null
            ];
        } catch (RequestException $e) {
            throw new \Exception('Error with alternative endpoint: ' . $e->getMessage());
        }
    }

    /**
     * Check if we should retry with alternative endpoint
     */
    protected function shouldRetryWithAlternativeEndpoint(RequestException $e): bool
    {
        if (!$e->hasResponse()) {
            return false;
        }

        $response = $e->getResponse();
        $statusCode = $response->getStatusCode();

        // Retry on specific error codes that might indicate consumer account issues
        if ($statusCode === 400 || $statusCode === 404) {
            $body = json_decode($response->getBody()->getContents(), true);
            $errorCode = $body['error']['code'] ?? '';

            return in_array($errorCode, [
                'ResourceNotFound',
                'BadRequest',
                'InvalidRequest'
            ]);
        }

        return false;
    }
    private function handleApiError(RequestException $e): void
    {
        if ($e->hasResponse()) {
            $response = $e->getResponse();
            $body = json_decode($response->getBody()->getContents(), true);

            $errorCode = $body['error']['code'] ?? 'Unknown';
            $errorMessage = $body['error']['message'] ?? $e->getMessage();

            // Handle token expiration
            if ($errorCode === 'InvalidAuthenticationToken' || $response->getStatusCode() === 401) {
                // Try to refresh token
                if ($this->oauthService->needsRefresh()) {
                    $refreshed = $this->oauthService->ensureValidToken();
                    if ($refreshed) {
                        $this->initializeService(); // Reinitialize with new token
                        return;
                    }
                }
                throw new \Exception('Authentication failed: ' . $errorMessage);
            }

            throw new \Exception("OneDrive API Error [{$errorCode}]: {$errorMessage}");
        }
    }

    /**
     * Get file icon URL based on mime type
     */
    private function getFileIconUrl(string $mimeType, bool $isFolder = false): string
    {
        if ($isFolder) {
            return 'https://cdn.microsoft.com/office/media/icons/folder-16.svg';
        }

        // Map common mime types to Microsoft Office icons
        $iconMap = [
            'application/pdf' => 'pdf-16.svg',
            'application/msword' => 'word-16.svg',
            'application/vnd.openxmlformats-officedocument.wordprocessingml.document' => 'word-16.svg',
            'application/vnd.ms-excel' => 'excel-16.svg',
            'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' => 'excel-16.svg',
            'application/vnd.ms-powerpoint' => 'powerpoint-16.svg',
            'application/vnd.openxmlformats-officedocument.presentationml.presentation' => 'powerpoint-16.svg',
            'text/plain' => 'text-16.svg',
            'image/jpeg' => 'photo-16.svg',
            'image/png' => 'photo-16.svg',
            'image/gif' => 'photo-16.svg',
            'video/mp4' => 'video-16.svg',
            'audio/mp3' => 'audio-16.svg',
            'application/zip' => 'zip-16.svg'
        ];

        $icon = $iconMap[$mimeType] ?? 'genericfile-16.svg';
        return "https://cdn.microsoft.com/office/media/icons/{$icon}";
    }

    /**
     * Format file size in human readable format
     */
    private function formatFileSize(int $bytes): string
    {
        if ($bytes === 0) return '0 B';

        $units = ['B', 'KB', 'MB', 'GB', 'TB'];
        $power = floor(log($bytes, 1024));
        $power = min($power, count($units) - 1);

        $size = $bytes / pow(1024, $power);
        $formattedSize = round($size, 2);

        return $formattedSize . ' ' . $units[$power];
    }

    /**
     * Get supported MIME types for OneDrive
     */
    public function getSupportedMimeTypes(): array
    {
        return [
            // Documents
            'application/pdf',
            'application/msword',
            'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
            'application/vnd.ms-excel',
            'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
            'application/vnd.ms-powerpoint',
            'application/vnd.openxmlformats-officedocument.presentationml.presentation',
            'text/plain',
            'text/csv',
            'application/rtf',

            // Images
            'image/jpeg',
            'image/png',
            'image/gif',
            'image/bmp',
            'image/webp',
            'image/svg+xml',
            'image/tiff',

            // Videos
            'video/mp4',
            'video/avi',
            'video/mov',
            'video/wmv',
            'video/webm',
            'video/mkv',
            'video/3gp',

            // Audio
            'audio/mp3',
            'audio/wav',
            'audio/ogg',
            'audio/m4a',
            'audio/aac',
            'audio/flac',

            // Archives
            'application/zip',
            'application/x-rar-compressed',
            'application/x-7z-compressed',
            'application/x-tar',
            'application/gzip',

            // Code/Text files
            'application/json',
            'application/xml',
            'text/html',
            'text/css',
            'text/javascript',
            'application/javascript',
            'text/x-php',
            'text/x-python',
            'text/x-java',
            'text/x-c',
            'text/x-csharp',

            // Other
            'application/octet-stream',

            // Folders
            'folder'
        ];
    }

    public function downloadFile(string $fileId)
    {
        $this->ensureValidToken();

        if (!$this->httpClient) {
            $this->initializeService();
        }

        try {
            $endpoint = $fileId === 'root'
                ? '/me/drive/root/content'
                : "/me/drive/items/{$fileId}/content";

            $endpoint = $this->baseUrl . $endpoint;

            $response = $this->httpClient->get($endpoint);
            return $response->getBody()->getContents();
        } catch (\Exception $e) {
            throw new \Exception('Error downloading file: ' . $e->getMessage());
        }
    }
}
