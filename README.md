# OAuth Manager

Un paquete Laravel completo para gestionar autenticación OAuth con múltiples servicios externos. Simplifica la integración con APIs como Google Drive, OneDrive, YouTube, Dropbox, Mega y más.

## Características

-  ✅ **Gestión automática de tokens** - Renovación automática de tokens expirados
-  ✅ **Múltiples servicios** - Soporte para varios servicios OAuth del mismo tipo
-  ✅ **Seguridad integrada** - Encriptación opcional de credenciales sensibles
-  ✅ **Interfaz de administración** - Comandos Artisan para testing y gestión
-  ✅ **Extensible** - Fácil adición de nuevos proveedores OAuth
-  ✅ **Testing completo** - Suite de tests unitarios y de integración
-  ✅ **Configuración flexible** - Middleware y rutas personalizables

## Requisitos

-  PHP 8.2+
-  Laravel 12.0+
-  Base de datos compatible con JSON (MySQL 5.7+, PostgreSQL, SQLite)

## Instalación

### 1. Instalar el paquete

```bash
composer require lbcdev/oauth-manager
```

### 2. Publicar archivos de configuración

```bash
# Publicar solo la configuración
php artisan vendor:publish --tag=oauth-manager-config

# Publicar todo (configuración, migraciones, seeders)
php artisan vendor:publish --tag=oauth-manager
```

### 3. Ejecutar migraciones

```bash
php artisan migrate
```

## Configuración

### Configuración básica

El archivo `config/oauth-manager.php` contiene la configuración principal:

```php
return [
    'services' => [
        'google_drive' => [
            'name' => 'Google Drive',
            'icon' => 'heroicon-o-folder',
            'provider' => \LBCDev\OAuthManager\Providers\GoogleDriveProvider::class,
            'scopes' => [
                'https://www.googleapis.com/auth/drive.file',
                'https://www.googleapis.com/auth/drive.metadata.readonly'
            ],
            'fields' => [
                'client_id' => env('GOOGLE_DRIVE_CLIENT_ID'),
                'client_secret' => env('GOOGLE_DRIVE_CLIENT_SECRET'),
                'redirect_uri' => env('GOOGLE_DRIVE_REDIRECT_URI'),
                'use_fwd_service_for_local_oauth' => env('GOOGLE_DRIVE_USE_FWD_SERVICE_FOR_LOCAL_OAUTH', false),
            ]
        ],
        'onedrive' => [
            'name' => 'OneDrive',
            'slug' => 'onedrive',
            'icon' => 'heroicon-o-cloud',
            'provider' => \LBCDev\OAuthManager\Providers\OneDriveProvider::class,
            'scopes' => [
                'https://graph.microsoft.com/Files.Read',
                'https://graph.microsoft.com/Files.ReadWrite',
                'https://graph.microsoft.com/User.Read'
            ],
            'fields' => [
                'client_id' => env('ONEDRIVE_CLIENT_ID'),
                'client_secret' => env('ONEDRIVE_CLIENT_SECRET'),
                'redirect_uri' => env('ONEDRIVE_REDIRECT_URI'),
                'use_fwd_service_for_local_oauth' => env('ONEDRIVE_USE_FWD_SERVICE_FOR_LOCAL_OAUTH', false),
            ]
        ],
        'dropbox' => [
            'name' => 'Dropbox',
            'slug' => 'dropbox',
            'icon' => 'heroicon-o-cloud',
            'provider' => \LBCDev\OAuthManager\Providers\DropboxProvider::class,
            'scopes' => [
                'files.content.write',
                'files.content.read',
                'files.metadata.read'
            ],
            'fields' => [
                'client_id' => env('DROPBOX_CLIENT_ID'),
                'client_secret' => env('DROPBOX_CLIENT_SECRET'),
                'redirect_uri' => env('DROPBOX_REDIRECT_URI'),
                'use_fwd_service_for_local_oauth' => env('DROPBOX_USE_FWD_SERVICE_FOR_LOCAL_OAUTH', false),
            ]
        ],
        'youtube' => [
            'name' => 'YouTube',
            'slug' => 'youtube',
            'icon' => 'heroicon-o-play',
            'provider' => \LBCDev\OAuthManager\Providers\YouTubeProvider::class,
            'scopes' => [
                'https://www.googleapis.com/auth/youtube.readonly',
                'https://www.googleapis.com/auth/youtube.upload',
                'https://www.googleapis.com/auth/youtube.force-ssl'
            ],
            'fields' => [
                'client_id' => env('YOUTUBE_CLIENT_ID'),
                'client_secret' => env('YOUTUBE_CLIENT_SECRET'),
                'redirect_uri' => env('YOUTUBE_REDIRECT_URI'),
                'use_fwd_service_for_local_oauth' => env('YOUTUBE_USE_FWD_SERVICE_FOR_LOCAL_OAUTH', false),
            ]
        ],
    ],
    'callback_route' => 'oauth-manager.callback',
    'middleware' => ['web'],
];
```

### Obtención de credenciales OAuth

Consultar la documentación en [docs/oauth_configuration.md](docs/oauth_configuration.md) para crear credenciales OAuth para cada servicio.

### Variables de entorno

Añade a tu archivo `.env`:

```env
# Google Drive
GOOGLE_DRIVE_CLIENT_ID=tu-client-id
GOOGLE_DRIVE_CLIENT_SECRET=tu-client-secret

# OneDrive
ONEDRIVE_CLIENT_ID=tu-client-id
ONEDRIVE_CLIENT_SECRET=tu-client-secret

# Dropbox
DROPBOX_CLIENT_ID=tu-app-key
DROPBOX_CLIENT_SECRET=tu-app-secret

# YouTube
YOUTUBE_CLIENT_ID=tu-client-id
YOUTUBE_CLIENT_SECRET=tu-client-secret

# Mega
MEGA_EMAIL=<EMAIL>
MEGA_PASSWORD=tu-password-mega

# Para desarrollo local usando fwd.host (opcional)
GOOGLE_DRIVE_USE_FWD_SERVICE_FOR_LOCAL_OAUTH=true
ONEDRIVE_USE_FWD_SERVICE_FOR_LOCAL_OAUTH=true
DROPBOX_USE_FWD_SERVICE_FOR_LOCAL_OAUTH=true
YOUTUBE_USE_FWD_SERVICE_FOR_LOCAL_OAUTH=true

# URL de redirección por defecto después del callback OAuth (opcional)
OAUTH_MANAGER_DEFAULT_REDIRECT_URL=/admin
```

### Control de redirección después del callback OAuth

El paquete ofrece múltiples opciones para controlar a dónde se redirige al usuario después de completar la autorización OAuth:

#### 1. URL de redirección por defecto (configuración global)

Configura una URL por defecto en tu archivo `.env`:

```env
OAUTH_MANAGER_DEFAULT_REDIRECT_URL=/admin
```

#### 2. URL de redirección por parámetro (por solicitud)

Pasa la URL de redirección como parámetro al iniciar la autorización:

```php
// Redirigir para autorización con URL de redirección específica
$redirectUrl = '/admin/oauth-services';
return redirect()->route('oauth-manager.authorize', [
    'service' => $service,
    'redirect_url' => $redirectUrl
]);
```

#### 3. Prioridad de redirección

El sistema sigue esta prioridad para determinar la redirección:

1. **URL pasada como parámetro** (mayor prioridad)
2. **URL configurada en `OAUTH_MANAGER_DEFAULT_REDIRECT_URL`**
3. **`redirect()->back()`** (comportamiento original, menor prioridad)

#### 4. Ejemplos de uso

**Para Filament Admin:**

```php
// En tu recurso de Filament
public function authorizeService($serviceId)
{
    $service = OAuthService::find($serviceId);

    return redirect()->route('oauth-manager.authorize', [
        'service' => $service,
        'redirect_url' => '/admin/oauth-services'
    ]);
}
```

**Para aplicaciones personalizadas:**

```php
// En tu controlador
public function connectService(Request $request)
{
    $service = OAuthService::find($request->service_id);
    $returnUrl = $request->get('return_url', '/dashboard');

    return redirect()->route('oauth-manager.authorize', [
        'service' => $service,
        'redirect_url' => $returnUrl
    ]);
}
```

### Desarrollo Local con fwd.host

Para desarrollo local, Google OAuth requiere URLs HTTPS válidas. El servicio `fwd.host` permite redirigir URLs públicas a tu entorno local.

**Configuración:**

1. **En tu .env local:**

   ```env
   GOOGLE_DRIVE_USE_FWD_SERVICE_FOR_LOCAL_OAUTH=true
   ```

2. **En Google Cloud Console, registra estas redirect URIs:**

   ```
   https://fwd.host/https://tu-dominio.test/oauth-manager/callback/servicio-1
   https://fwd.host/https://tu-dominio.test/oauth-manager/callback/servicio-2
   ```

3. **Cómo funciona:**
   -  **Local (fwd.host):** `https://fwd.host/https://dominio.test/oauth-manager/callback/mi-servicio`
   -  **Producción:** `https://tu-dominio.com/oauth-manager/callback/mi-servicio`

**Ventajas:**

-  Un solo cliente OAuth para múltiples servicios
-  Cada servicio tiene su propia redirect URI única basada en el slug
-  Funciona tanto en local como en producción

## Uso básico

### Crear un servicio OAuth

```php
use LBCDev\OAuthManager\Models\OAuthService;

// Crear servicio OAuth
$oauthService = OAuthService::create([
    'name' => 'Mi cuenta de Google Drive',
    'service_type' => 'google_drive', // google_drive, onedrive, dropbox, youtube, mega
    'slug' => 'mi-google-drive',
    'credentials' => [
        'client_id' => 'tu-client-id',
        'client_secret' => 'tu-client-secret',
    ],
    'is_active' => true,
]);
```

### Autorizar un servicio

```php
// Redirigir al usuario para autorización
return redirect()->route('oauth-manager.authorize', ['service' => $service]);
```

## Cómo obtener un token de acceso válido

### Flujo completo de autorización OAuth

Para obtener un token de acceso válido, necesitas implementar el flujo completo de OAuth.

```mermaid
sequenceDiagram
    Usuario->>+Paquete: Inicia autorización
    Paquete->>+Proveedor: Redirige a login OAuth
    Proveedor-->>-Paquete: Retorna código auth
    Paquete->>+Proveedor: Intercambia código por token
    Proveedor-->>-Paquete: Retorna access/refresh tokens
    Paquete->>+App: Token válido listo para usar
```

Aquí te mostramos cómo hacerlo paso a paso:

#### 1. Crear el controlador

```php
<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Http\RedirectResponse;
use LBCDev\OAuthManager\Models\OAuthService;

class OAuthController extends Controller
{
    /**
     * Mostrar la página de servicios OAuth disponibles
     */
    public function index()
    {
        $services = OAuthService::where('is_active', true)->get();

        return view('oauth.index', compact('services'));
    }

    /**
     * Crear un nuevo servicio OAuth y redirigir para autorización
     */
    public function connect(Request $request): RedirectResponse
    {
        $request->validate([
            'service_type' => 'required|in:google_drive,onedrive,dropbox,youtube,mega',
            'name' => 'required|string|max:255',
        ]);

        // Crear el servicio OAuth
        $service = OAuthService::create([
            'name' => $request->name,
            'service_type' => $request->service_type,
            'slug' => $request->service_type . '-' . auth()->id() . '-' . time(),
            'credentials' => $this->getCredentialsForService($request->service_type),
            'is_active' => true,
        ]);

        // Redirigir para autorización OAuth
        return redirect()->route('oauth-manager.authorize', [
            'service' => $service,
            'redirect_url' => route('oauth.success', ['service' => $service->id])
        ]);
    }

    /**
     * Página de éxito después de la autorización
     */
    public function success(Request $request, OAuthService $service)
    {
        // Verificar que el servicio tiene un token válido
        if ($service->ensureValidToken()) {
            return view('oauth.success', compact('service'));
        }

        return redirect()->route('oauth.index')
            ->with('error', 'No se pudo obtener el token de acceso.');
    }

    /**
     * Obtener las credenciales para un tipo de servicio
     */
    private function getCredentialsForService(string $serviceType): array
    {
        return match($serviceType) {
            'google_drive' => [
                'client_id' => config('oauth-manager.services.google_drive.fields.client_id'),
                'client_secret' => config('oauth-manager.services.google_drive.fields.client_secret'),
            ],
            'onedrive' => [
                'client_id' => config('oauth-manager.services.onedrive.fields.client_id'),
                'client_secret' => config('oauth-manager.services.onedrive.fields.client_secret'),
            ],
            'dropbox' => [
                'client_id' => config('oauth-manager.services.dropbox.fields.client_id'),
                'client_secret' => config('oauth-manager.services.dropbox.fields.client_secret'),
            ],
            'youtube' => [
                'client_id' => config('oauth-manager.services.youtube.fields.client_id'),
                'client_secret' => config('oauth-manager.services.youtube.fields.client_secret'),
            ],
            'mega' => [
                'email' => env('MEGA_EMAIL'),
                'password' => env('MEGA_PASSWORD'),
            ],
            default => []
        };
    }
}
```

#### 2. Crear las rutas

```php
// routes/web.php
use App\Http\Controllers\OAuthController;

Route::middleware(['auth'])->group(function () {
    Route::get('/oauth', [OAuthController::class, 'index'])->name('oauth.index');
    Route::post('/oauth/connect', [OAuthController::class, 'connect'])->name('oauth.connect');
    Route::get('/oauth/success/{service}', [OAuthController::class, 'success'])->name('oauth.success');
});
```

#### 3. Crear la vista principal (resources/views/oauth/index.blade.php)

```blade
@extends('layouts.app')

@section('content')
<div class="container">
    <div class="row justify-content-center">
        <div class="col-md-8">
            <div class="card">
                <div class="card-header">{{ __('Conectar Servicios OAuth') }}</div>

                <div class="card-body">
                    @if (session('error'))
                        <div class="alert alert-danger">
                            {{ session('error') }}
                        </div>
                    @endif

                    @if (session('success'))
                        <div class="alert alert-success">
                            {{ session('success') }}
                        </div>
                    @endif

                    <h5>Servicios Conectados</h5>
                    @if($services->count() > 0)
                        <div class="row mb-4">
                            @foreach($services as $service)
                                <div class="col-md-6 mb-3">
                                    <div class="card">
                                        <div class="card-body">
                                            <h6 class="card-title">{{ $service->name }}</h6>
                                            <p class="card-text">
                                                <small class="text-muted">{{ ucfirst(str_replace('_', ' ', $service->service_type)) }}</small>
                                            </p>
                                            <span class="badge {{ $service->access_token ? 'bg-success' : 'bg-warning' }}">
                                                {{ $service->access_token ? 'Conectado' : 'Pendiente' }}
                                            </span>
                                            @if($service->access_token)
                                                <p class="mt-2 mb-0">
                                                    <small>Última conexión: {{ $service->last_used_at?->diffForHumans() ?? 'Nunca' }}</small>
                                                </p>
                                            @endif
                                        </div>
                                    </div>
                                </div>
                            @endforeach
                        </div>
                    @else
                        <p class="text-muted">No tienes servicios conectados.</p>
                    @endif

                    <h5>Conectar Nuevo Servicio</h5>
                    <form method="POST" action="{{ route('oauth.connect') }}">
                        @csrf

                        <div class="mb-3">
                            <label for="service_type" class="form-label">Tipo de Servicio</label>
                            <select class="form-select @error('service_type') is-invalid @enderror"
                                    id="service_type" name="service_type" required>
                                <option value="">Selecciona un servicio</option>
                                <option value="google_drive">Google Drive</option>
                                <option value="onedrive">OneDrive</option>
                                <option value="dropbox">Dropbox</option>
                                <option value="youtube">YouTube</option>
                                <option value="mega">Mega</option>
                            </select>
                            @error('service_type')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>

                        <div class="mb-3">
                            <label for="name" class="form-label">Nombre del Servicio</label>
                            <input type="text"
                                   class="form-control @error('name') is-invalid @enderror"
                                   id="name"
                                   name="name"
                                   placeholder="Ej: Mi cuenta de Google Drive"
                                   value="{{ old('name') }}"
                                   required>
                            @error('name')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>

                        <button type="submit" class="btn btn-primary">
                            Conectar Servicio
                        </button>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection
```

#### 4. Crear la vista de éxito (resources/views/oauth/success.blade.php)

```blade
@extends('layouts.app')

@section('content')
<div class="container">
    <div class="row justify-content-center">
        <div class="col-md-8">
            <div class="card">
                <div class="card-header">{{ __('Servicio Conectado Exitosamente') }}</div>

                <div class="card-body">
                    <div class="alert alert-success">
                        <h5>¡Conexión exitosa!</h5>
                        <p>El servicio <strong>{{ $service->name }}</strong> se ha conectado correctamente.</p>
                    </div>

                    <div class="card">
                        <div class="card-body">
                            <h6>Información del Servicio</h6>
                            <ul class="list-unstyled">
                                <li><strong>Nombre:</strong> {{ $service->name }}</li>
                                <li><strong>Tipo:</strong> {{ ucfirst(str_replace('_', ' ', $service->service_type)) }}</li>
                                <li><strong>Estado:</strong>
                                    <span class="badge bg-success">Conectado</span>
                                </li>
                                <li><strong>Token válido hasta:</strong>
                                    {{ $service->expires_at ? $service->expires_at->format('d/m/Y H:i') : 'Sin expiración' }}
                                </li>
                            </ul>
                        </div>
                    </div>

                    <div class="mt-3">
                        <a href="{{ route('oauth.index') }}" class="btn btn-primary">
                            Volver a Servicios
                        </a>

                        <button type="button" class="btn btn-secondary" onclick="testConnection()">
                            Probar Conexión
                        </button>
                    </div>

                    <div id="test-result" class="mt-3" style="display: none;">
                        <!-- Resultado de la prueba de conexión -->
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
async function testConnection() {
    const resultDiv = document.getElementById('test-result');
    resultDiv.style.display = 'block';
    resultDiv.innerHTML = '<div class="alert alert-info">Probando conexión...</div>';

    try {
        const response = await fetch(`/oauth/test/{{ $service->id }}`, {
            method: 'POST',
            headers: {
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content'),
                'Content-Type': 'application/json',
            }
        });

        const result = await response.json();

        if (result.success) {
            resultDiv.innerHTML = '<div class="alert alert-success">✅ Conexión exitosa</div>';
        } else {
            resultDiv.innerHTML = '<div class="alert alert-danger">❌ Error de conexión: ' + result.message + '</div>';
        }
    } catch (error) {
        resultDiv.innerHTML = '<div class="alert alert-danger">❌ Error de conexión: ' + error.message + '</div>';
    }
}
</script>
@endsection
```

### Obtener y usar tokens en tu aplicación

Una vez que tienes servicios conectados, puedes obtener tokens válidos de esta manera:

```php
use LBCDev\OAuthManager\Models\OAuthService;

// Obtener token del primer servicio activo del tipo
$googleService = OAuthService::where('service_type', 'google_drive')
    ->where('is_active', true)
    ->first();

if ($googleService && $googleService->ensureValidToken()) {
    $googleToken = $googleService->access_token;
    // Usar el token...
}

// Obtener token de un servicio específico por nombre
$googleService = OAuthService::where('service_type', 'google_drive')
    ->where('name', 'Mi cuenta de Google Drive')
    ->where('is_active', true)
    ->first();

if ($googleService && $googleService->ensureValidToken()) {
    $googleToken = $googleService->access_token;
    // Usar el token...
}
```

## Ejemplos de uso

Para ver ejemplos detallados de cómo usar los tokens con cada servicio, consulta los archivos en la carpeta `docs/examples/`:

-  **Google Drive**: `docs/examples/google-drive-usage.php` - Ejemplos completos para subir, descargar, listar y buscar archivos
-  **OneDrive**: `docs/examples/onedrive-usage.php` - Interacción directa con Microsoft Graph API
-  **Dropbox**: `docs/examples/dropbox-usage-api.php` - Uso de la API de Dropbox para gestión de archivos
-  **YouTube**: `docs/examples/youtube-usage-api.php` - Acceso a YouTube Data API v3 para canales y videos
-  **Mega**: `docs/examples/mega-usage-provider.php` - Uso del MegaProvider con métodos específicos disponibles

> **Nota importante**: Los providers han sido simplificados y solo incluyen los métodos básicos requeridos: `getAuthorizationUrl()`, `handleCallback()`, `refreshToken()`, y `revokeToken()`. Métodos adicionales como `testConnection()`, `getUserInfo()`, `getFiles()`, etc. solo están disponibles en **MegaProvider**. Para otros servicios, usa directamente sus APIs correspondientes como se muestra en los ejemplos.

### Ejemplo básico de uso

```php
use LBCDev\OAuthManager\Models\OAuthService;

// Obtener un servicio OAuth activo
$service = OAuthService::where('service_type', 'google_drive')
    ->where('is_active', true)
    ->first();

if ($service && $service->ensureValidToken()) {
    // El token está listo para usar
    $token = $service->access_token;

    // Usar el token con la API correspondiente
    // Ver ejemplos específicos en docs/examples/
}
```

### Revocar tokens de acceso

```php
use LBCDev\OAuthManager\Models\OAuthService;

// Revocar token de un servicio específico por tipo
$service = OAuthService::where('service_type', 'google_drive')
    ->where('is_active', true)
    ->first();

if ($service) {
    $success = $service->revokeToken();
}

// Revocar token de un servicio específico por tipo y nombre
$service = OAuthService::where('service_type', 'google_drive')
    ->where('name', 'Mi cuenta de Google Drive')
    ->where('is_active', true)
    ->first();

if ($service) {
    $success = $service->revokeToken();
}

// Revocar token por ID
$service = OAuthService::find(1);
if ($service) {
    $success = $service->revokeToken();
}
```

**Comportamiento de la revocación:**

-  **Revocación remota**: Se intenta revocar el token en el proveedor OAuth (Google, Microsoft, etc.)
-  **Limpieza local**: Los tokens se eliminan de la base de datos local **independientemente** del resultado de la revocación remota
-  **Preservación de datos**: Solo se eliminan los tokens; otros datos del servicio (credenciales, configuración) se mantienen
-  **Valor de retorno**: `true` si la revocación remota fue exitosa, `false` si falló (pero los tokens locales se limpian en ambos casos)

### Usar el provider directamente

```php
use LBCDev\OAuthManager\Models\OAuthService;

$service = OAuthService::where('service_type', 'mega')->first();
$provider = $service->getProviderInstance();

// Solo MegaProvider tiene métodos adicionales disponibles:
if ($service->service_type === 'mega') {
    // Obtener archivos usando el provider
    $files = $provider->getFiles(); // Archivos de la raíz
    $files = $provider->getFiles('folder-id'); // Archivos de una carpeta específica

    // Obtener información del usuario
    $userInfo = $provider->getUserInfo();

    // Test de conexión
    $isConnected = $provider->testConnection();
}

// Para otros providers, usar directamente las APIs correspondientes
// Ver ejemplos en docs/examples/
```

## Comandos Artisan

### Probar conexión OAuth

```bash
# Probar conexión de un servicio específico
# Para Mega: ejecuta testConnection()
# Para otros providers: verifica que el token sea válido
php artisan oauth:test {service_id}

# Probar y refrescar token automáticamente si es necesario
php artisan oauth:test {service_id} --refresh
```

### Refrescar tokens

```bash
# Refrescar token de un servicio específico
php artisan oauth:refresh {service_id}

# Refrescar todos los tokens que lo necesiten
php artisan oauth:refresh --all
```

### Revocar tokens

```bash
# Revocar token de un servicio específico
php artisan oauth:revoke {service_id}

# Revocar todos los tokens activos
php artisan oauth:revoke --all

# Revocar todos los tokens de un tipo específico
php artisan oauth:revoke --type=google_drive
```

### Mostrar información de servicios

```bash
# Mostrar información de todos los servicios OAuth
php artisan oauth:show

# Mostrar información de un servicio específico por slug
php artisan oauth:show {slug}

# Mostrar tokens completos sin truncar (usar con precaución)
php artisan oauth:show --full-tokens
php artisan oauth:show {slug} --full-tokens
```

**Información mostrada:**

-  Nombre y slug del servicio
-  Tipo de servicio (google_drive, dropbox, etc.)
-  Estado (activo/inactivo)
-  Estado del token (válido, expirado, no autorizado)
-  Access token y refresh token (truncados por seguridad)
-  Fechas de expiración, último uso, creación y actualización

## Modelo OAuthService

### Propiedades principales

```php
$service = OAuthService::find(1);

$service->name;           // Nombre del servicio
$service->service_type;   // Tipo de servicio (google_drive, dropbox, etc.)
$service->slug;           // Slug único
$service->credentials;    // Array con credenciales
$service->access_token;   // Token de acceso actual
$service->refresh_token;  // Token de renovación
$service->expires_at;     // Fecha de expiración
$service->is_active;      // Si está activo
$service->last_used_at;   // Última vez usado
```

### Métodos útiles

```php
// Verificar si el token está expirado
$service->isTokenExpired();

// Verificar si necesita renovación
$service->needsRefresh();

// Obtener instancia del proveedor
$provider = $service->getProviderInstance();
```

## Extender con nuevos proveedores

### 1. Crear el proveedor

```php
<?php

namespace App\OAuth\Providers;

use LBCDev\OAuthManager\Providers\BaseOAuthProvider;

class CustomProvider extends BaseOAuthProvider
{
    public function getAuthorizationUrl(): string
    {
        // Implementar lógica de autorización
    }

    public function handleCallback(string $code): array
    {
        // Manejar callback y retornar tokens
        return [
            'access_token' => $token->getToken(),
            'refresh_token' => $token->getRefreshToken(),
            'expires_at' => $token->getExpires() ? now()->addSeconds($token->getExpires() - time()) : null,
        ];
    }

    public function refreshToken(): ?array
    {
        // Implementar renovación de token
    }

    public function revokeToken(): bool
    {
        // Implementar revocación de token
    }
}
```

### 2. Registrar en configuración

```php
// config/oauth-manager.php
'services' => [
    'custom_service' => [
        'name' => 'Custom Service',
        'icon' => 'heroicon-o-star',
        'provider' => App\OAuth\Providers\CustomProvider::class,
        'scopes' => ['read', 'write'],
        'fields' => [
            'client_id' => 'Client ID',
            'client_secret' => 'Client Secret',
        ]
    ],
],
```

## Autor

**Luis BC** - [<EMAIL>](mailto:<EMAIL>)
