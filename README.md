# Laravel OAuth File Explorer

[![Latest Stable Version](https://poser.pugx.org/lbcdev/oauth-file-explorer/v/stable)](https://packagist.org/packages/lbcdev/oauth-file-explorer)
[![Total Downloads](https://poser.pugx.org/lbcdev/oauth-file-explorer/downloads)](https://packagist.org/packages/lbcdev/oauth-file-explorer)
[![License](https://poser.pugx.org/lbcdev/oauth-file-explorer/license)](https://packagist.org/packages/lbcdev/oauth-file-explorer)

Un componente Livewire extensible para explorar archivos en servicios de almacenamiento en la nube usando OAuth. Soporta múltiples proveedores de almacenamiento con una interfaz unificada.

## Características

-  🔐 **Autenticación OAuth** - Integración segura con servicios de almacenamiento
-  🗂️ **Múltiples Proveedores** - Google Drive, Dropbox, OneDrive y más
-  🎨 **Componente Livewire** - Interfaz reactiva y moderna
-  🔍 **Búsqueda de Archivos** - Busca archivos en todos los servicios
-  📁 **Navegación de Carpetas** - Navega por la estructura de carpetas
-  🎯 **Filtros de Tipo** - Filtra archivos por tipo MIME
-  🧪 **Completamente Probado** - Suite completa de tests unitarios y de integración
-  🔧 **Extensible** - Fácil de extender con nuevos proveedores

## Servicios Soportados

| Servicio         | Estado               | Descripción                                                  |
| ---------------- | -------------------- | ------------------------------------------------------------ |
| **Google Drive** | ✅ Completo          | Acceso completo a archivos y carpetas                        |
| **Dropbox**      | ✅ Completo          | Navegación y descarga de archivos                            |
| **OneDrive**     | 🚧 Base Implementada | Estructura base lista, requiere configuración OAuth completa |
| **YouTube**      | 🚧 Planificado       | Acceso a videos y listas de reproducción                     |
| **Mega**         | 🚧 Planificado       | Almacenamiento encriptado                                    |

## Instalación

### Requisitos

-  PHP 8.2+
-  Laravel 10.0+
-  Livewire 3.0+

### Instalar via Composer

```bash
composer require lbcdev/oauth-file-explorer
```

### Instalar el paquete OAuth Manager (dependencia)

```bash
composer require lbcdev/oauth-manager
```

### Publicar Configuración

```bash
php artisan vendor:publish --tag=oauth-file-explorer-config
php artisan vendor:publish --tag=oauth-file-explorer-views
```

### Ejecutar Migraciones

```bash
php artisan migrate
```

## Configuración

### 1. Configurar Servicios OAuth

Edita `config/oauth-file-explorer.php`:

```php
return [
    'default_page_size' => 50,

    'supported_services' => [
        'google_drive',
        'dropbox',
        'onedrive',
        'youtube',
        'mega',
    ],

    'explorers' => [
        'google_drive' => \LBCDev\OAuthFileExplorer\Services\Explorers\GoogleDriveExplorer::class,
        'dropbox' => \LBCDev\OAuthFileExplorer\Services\Explorers\DropboxExplorer::class,
        'onedrive' => \LBCDev\OAuthFileExplorer\Services\Explorers\OneDriveExplorer::class,
    ],
];
```

### 2. Configurar Proveedores OAuth

#### Google Drive

1. Crear proyecto en [Google Cloud Console](https://console.cloud.google.com/)
2. Habilitar Google Drive API
3. Crear credenciales OAuth 2.0
4. Configurar en tu aplicación:

```php
// En tu controlador o servicio
use LBCDev\OAuthManager\Models\OAuthService;

$googleDrive = OAuthService::create([
    'name' => 'Mi Google Drive',
    'service_type' => 'google_drive',
    'config' => [
        'client_id' => 'tu-client-id',
        'client_secret' => 'tu-client-secret',
        'redirect_uri' => 'https://tu-app.com/oauth/callback',
        'scopes' => ['https://www.googleapis.com/auth/drive.readonly']
    ]
]);
```

#### OneDrive

1. Registrar aplicación en [Azure Portal](https://portal.azure.com/)
2. Configurar permisos para Microsoft Graph
3. Configurar en tu aplicación:

```php
$oneDrive = OAuthService::create([
    'name' => 'Mi OneDrive',
    'service_type' => 'onedrive',
    'config' => [
        'client_id' => 'tu-client-id',
        'client_secret' => 'tu-client-secret',
        'tenant_id' => 'common', // o tu tenant específico
        'redirect_uri' => 'https://tu-app.com/oauth/callback',
        'scopes' => ['https://graph.microsoft.com/Files.Read']
    ]
]);
```

#### Dropbox

1. Crear aplicación en [Dropbox App Console](https://www.dropbox.com/developers/apps)
2. Configurar en tu aplicación:

```php
$dropbox = OAuthService::create([
    'name' => 'Mi Dropbox',
    'service_type' => 'dropbox',
    'config' => [
        'client_id' => 'tu-client-id',
        'client_secret' => 'tu-client-secret',
        'redirect_uri' => 'https://tu-app.com/oauth/callback'
    ]
]);
```

## Uso

### Componente Livewire Básico

```blade
{{-- En tu vista Blade --}}
<livewire:oauth-file-explorer
    :oauth-service-id="$oauthServiceId"
    field-name="selected_file_url"
    :accepted-mime-types="['image/jpeg', 'image/png', 'application/pdf']"
/>
```

### Uso Programático

```php
use LBCDev\OAuthFileExplorer\Services\FileExplorerFactory;
use LBCDev\OAuthManager\Models\OAuthService;

// Obtener servicio OAuth
$oauthService = OAuthService::where('service_type', 'google_drive')->first();

// Crear explorer
$explorer = FileExplorerFactory::create($oauthService);

// Listar archivos
$files = $explorer->listFiles('root', 50);

// Buscar archivos
$searchResults = $explorer->searchFiles('documento');

// Obtener información de archivo
$fileInfo = $explorer->getFile('file-id');

// Obtener breadcrumb
$breadcrumb = $explorer->getBreadcrumb('folder-id');

// Probar conexión
$isConnected = $explorer->testConnection();
```

### Eventos Livewire

```php
// En tu componente Livewire
protected $listeners = ['fileSelected'];

public function fileSelected($fileData)
{
    // Manejar archivo seleccionado
    $this->selectedFile = $fileData;

    // El archivo contiene:
    // - id: ID único del archivo
    // - name: Nombre del archivo
    // - mimeType: Tipo MIME
    // - size: Tamaño en bytes
    // - webViewLink: URL para ver
    // - downloadUrl: URL para descargar
    // - serviceType: Tipo de servicio (google_drive, dropbox, etc.)
}
```

## Personalización

### Crear Explorer Personalizado

```php
use LBCDev\OAuthFileExplorer\Services\AbstractFileExplorer;

class MiCustomExplorer extends AbstractFileExplorer
{
    public function getServiceType(): string
    {
        return 'mi_servicio';
    }

    protected function initializeService(): void
    {
        // Inicializar tu servicio
    }

    public function listFiles(string $folderId = 'root', int $pageSize = 50): array
    {
        // Implementar listado de archivos
    }

    // Implementar otros métodos requeridos...
}
```

### Registrar Explorer Personalizado

```php
// En config/oauth-file-explorer.php
'explorers' => [
    'mi_servicio' => \App\Services\MiCustomExplorer::class,
],
```

## Testing

### Ejecutar Tests

```bash
# Todos los tests
vendor/bin/phpunit

# Tests específicos
vendor/bin/phpunit tests/Unit/OneDriveExplorerTest.php
vendor/bin/phpunit tests/Feature/OAuthFileExplorerComponentTest.php
```

### Cobertura de Tests

El paquete incluye tests completos para:

-  ✅ Explorers individuales (Google Drive, Dropbox, OneDrive)
-  ✅ Factory de explorers
-  ✅ Componente Livewire
-  ✅ Funcionalidad abstracta base
-  ✅ Manejo de errores
-  ✅ Formateo de datos

## API Reference

### FileExplorerInterface

```php
interface FileExplorerInterface
{
    public function listFiles(string $folderId = 'root', int $pageSize = 50): array;
    public function getFile(string $fileId): array;
    public function searchFiles(string $query, string $folderId = 'root'): array;
    public function getBreadcrumb(string $folderId): array;
    public function testConnection(): bool;
    public function getServiceType(): string;
    public function getSupportedMimeTypes(): array;
    public function supportsMimeType(string $mimeType): bool;
}
```

### Estructura de Datos de Archivo

```php
[
    'id' => 'unique-file-id',
    'name' => 'documento.pdf',
    'mimeType' => 'application/pdf',
    'isFolder' => false,
    'size' => 1024,
    'sizeFormatted' => '1.00 KB',
    'modifiedTime' => '2024-01-15T10:30:00Z',
    'webViewLink' => 'https://...',
    'webContentLink' => 'https://...',
    'downloadUrl' => 'https://...',
    'iconLink' => 'https://...',
    'parents' => ['parent-folder-id'],
    'serviceType' => 'google_drive'
]
```

## Estado de Implementación

### OneDrive

La implementación de OneDrive está en estado base. La estructura y los tests están listos, pero requiere:

1. **Configuración OAuth completa**: Implementar la autenticación real con Microsoft Graph
2. **Métodos de API**: Completar la integración con Microsoft Graph SDK
3. **Manejo de errores**: Implementar manejo específico de errores de OneDrive
4. **Tests de integración**: Agregar tests con datos reales (opcional)

Para completar la implementación de OneDrive:

```php
// En OneDriveExplorer.php, reemplazar los métodos placeholder con:
protected function initializeService(): void
{
    $this->ensureValidToken();

    $config = $this->oauthService->config ?? [];

    $tokenRequestContext = new AuthorizationCodeContext(
        $config['tenant_id'] ?? 'common',
        $config['client_id'],
        $config['client_secret'],
        $this->oauthService->access_token,
        $config['redirect_uri']
    );

    $scopes = ['https://graph.microsoft.com/Files.Read'];
    $this->graphClient = new GraphServiceClient($tokenRequestContext, $scopes);
}
```

## Contribuir

1. Fork el proyecto
2. Crear rama para feature (`git checkout -b feature/nueva-funcionalidad`)
3. Commit cambios (`git commit -am 'Agregar nueva funcionalidad'`)
4. Push a la rama (`git push origin feature/nueva-funcionalidad`)
5. Crear Pull Request

## Licencia

Este paquete es software de código abierto licenciado bajo la [Licencia MIT](LICENSE).

## Soporte

-  📧 Email: <EMAIL>
-  🐛 Issues: [GitHub Issues](https://github.com/lbcdev/oauth-file-explorer/issues)
-  📖 Documentación: [Wiki](https://github.com/lbcdev/oauth-file-explorer/wiki)

## Changelog

Ver [CHANGELOG.md](CHANGELOG.md) para detalles de cambios y versiones.
