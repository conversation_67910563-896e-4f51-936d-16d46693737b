<?php

// src/Providers/GoogleDriveProvider.php
namespace LBCDev\OAuthManager\Providers;

use League\OAuth2\Client\Provider\Google;
use League\OAuth2\Client\Token\AccessToken;

class GoogleDriveProvider extends BaseOAuthProvider
{
    protected function getProvider(): Google
    {
        if ($this->checkOAuthCredentials() === false) {
            throw new \Exception('Missing client credentials');
        }

        return new Google([
            'clientId' => $this->service->credentials['client_id'],
            'clientSecret' => $this->service->credentials['client_secret'],
            'redirectUri' => $this->getRedirectUri(),
        ]);
    }

    public function getAuthorizationUrl(): string
    {
        $provider = $this->getProvider();

        $authUrl = $provider->getAuthorizationUrl([
            'scope' => $this->config['scopes'],
            'access_type' => 'offline',
            'prompt' => 'consent',
            'include_granted_scopes' => 'true'
        ]);

        session(['oauth2state' => $provider->getState()]);

        return $authUrl;
    }

    public function handleCallback(string $code): array
    {
        $provider = $this->getProvider();

        $token = $provider->getAccessToken('authorization_code', [
            'code' => $code,
        ]);

        return [
            'access_token' => $token->getToken(),
            'refresh_token' => $token->getRefreshToken(),
            'expires_at' => $token->getExpires() ? now()->addSeconds($token->getExpires() - time()) : null,
        ];
    }

    public function refreshToken(): ?array
    {
        if (!$this->service->refresh_token) {
            return null;
        }

        $provider = $this->getProvider();

        try {
            $token = $provider->getAccessToken('refresh_token', [
                'refresh_token' => $this->service->refresh_token,
            ]);

            return [
                'access_token' => $token->getToken(),
                'refresh_token' => $token->getRefreshToken() ?: $this->service->refresh_token,
                'expires_at' => $token->getExpires() ? now()->addSeconds($token->getExpires() - time()) : null,
            ];
        } catch (\Exception) {
            // \Log::error('Failed to refresh Google Drive token: ' . $e->getMessage());
            return null;
        }
    }

    public function revokeToken(): bool
    {
        if (!$this->service->access_token) {
            return false;
        }

        try {
            $client = new \Google_Client();
            $client->setAccessToken($this->service->access_token);
            $client->revokeToken();

            return true;
        } catch (\Exception) {
            // \Log::error('Failed to revoke Google Drive token: ' . $e->getMessage());
            return false;
        }
    }

    // public function testConnection(): bool
    // {
    //     if (!$this->service->access_token) {
    //         return false;
    //     }

    //     try {
    //         $client = new \Google_Client();
    //         $client->setAccessToken($this->service->access_token);

    //         $driveService = new \Google\Service\Drive($client);

    //         // Simple test: get user's about info
    //         $about = $driveService->about->get(['fields' => 'user']);

    //         return !empty($about->getUser());
    //     } catch (\Exception) {
    //         // \Log::error('Google Drive connection test failed: ' . $e->getMessage());
    //         return false;
    //     }
    // }
}
