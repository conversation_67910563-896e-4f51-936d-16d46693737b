<?php

/**
 * Ejemplo de uso con Mega
 * 
 * Este archivo muestra cómo usar el token de acceso de Mega
 * utilizando los métodos disponibles en el MegaProvider.
 * 
 * NOTA: MegaProvider es el único provider que mantiene métodos
 * adicionales como testConnection(), getUserInfo() y getFiles().
 */

require_once __DIR__ . '/../../vendor/autoload.php';

use LBCDev\OAuthManager\Models\OAuthService;

// Obtener el servicio de Mega
$megaService = OAuthService::where('service_type', 'mega')
    ->where('is_active', true)
    ->first();

if (!$megaService) {
    echo "No se encontró ningún servicio de Mega activo.\n";
    exit(1);
}

// Asegurar que tenemos un token válido
if (!$megaService->ensureValidToken()) {
    echo "No se pudo obtener un token válido para Mega.\n";
    exit(1);
}

echo "Token válido obtenido para Mega.\n";

try {
    // Obtener la instancia del provider
    $provider = $megaService->getProviderInstance();

    // === EJEMPLO 1: Test de conexión ===
    echo "\n=== TEST DE CONEXIÓN ===\n";
    
    $isConnected = $provider->testConnection();
    
    if ($isConnected) {
        echo "✅ Conectado exitosamente a Mega.\n";
    } else {
        echo "❌ Error de conexión con Mega.\n";
        exit(1);
    }

    // === EJEMPLO 2: Obtener información del usuario ===
    echo "\n=== INFORMACIÓN DEL USUARIO ===\n";
    
    $userInfo = $provider->getUserInfo();
    
    if ($userInfo) {
        echo "Email: " . ($userInfo['email'] ?? 'N/A') . "\n";
        echo "Almacenamiento usado: " . formatBytes($userInfo['storage_used'] ?? 0) . "\n";
        echo "Almacenamiento total: " . formatBytes($userInfo['storage_total'] ?? 0) . "\n";
        echo "Número de archivos: " . ($userInfo['file_count'] ?? 0) . "\n";
        echo "Número de carpetas: " . ($userInfo['folder_count'] ?? 0) . "\n";
        
        if (isset($userInfo['storage_used']) && isset($userInfo['storage_total'])) {
            $usagePercent = ($userInfo['storage_used'] / $userInfo['storage_total']) * 100;
            echo "Porcentaje de uso: " . round($usagePercent, 2) . "%\n";
        }
        
        if (isset($userInfo['account_type'])) {
            echo "Tipo de cuenta: " . $userInfo['account_type'] . "\n";
        }
        
        if (isset($userInfo['created_at'])) {
            echo "Cuenta creada: " . $userInfo['created_at'] . "\n";
        }
    } else {
        echo "No se pudo obtener la información del usuario.\n";
    }

    // === EJEMPLO 3: Listar archivos y carpetas de la raíz ===
    echo "\n=== ARCHIVOS Y CARPETAS EN LA RAÍZ ===\n";
    
    $files = $provider->getFiles();
    
    if ($files && !empty($files)) {
        echo "Elementos encontrados: " . count($files) . "\n\n";
        
        $totalSize = 0;
        $fileCount = 0;
        $folderCount = 0;
        
        foreach ($files as $file) {
            $type = $file['type'] === 'file' ? '📄' : '📁';
            $name = $file['name'];
            $size = '';
            
            if ($file['type'] === 'file') {
                $fileCount++;
                if (isset($file['size'])) {
                    $size = ' (' . formatBytes($file['size']) . ')';
                    $totalSize += $file['size'];
                }
            } else {
                $folderCount++;
            }
            
            echo "{$type} {$name}{$size}\n";
            
            if (isset($file['created_at'])) {
                echo "   Creado: {$file['created_at']}\n";
            }
            
            if (isset($file['modified_at'])) {
                echo "   Modificado: {$file['modified_at']}\n";
            }
            
            if (isset($file['id'])) {
                echo "   ID: {$file['id']}\n";
            }
            
            echo "\n";
        }
        
        echo "Resumen:\n";
        echo "- Archivos: {$fileCount}\n";
        echo "- Carpetas: {$folderCount}\n";
        echo "- Tamaño total de archivos: " . formatBytes($totalSize) . "\n";
        
    } else {
        echo "No se encontraron archivos en la raíz o no se pudo acceder.\n";
    }

    // === EJEMPLO 4: Listar archivos de una carpeta específica ===
    echo "\n=== ARCHIVOS DE CARPETA ESPECÍFICA ===\n";
    
    // Buscar una carpeta en los archivos obtenidos anteriormente
    $folderId = null;
    if ($files) {
        foreach ($files as $file) {
            if ($file['type'] === 'folder') {
                $folderId = $file['id'] ?? null;
                $folderName = $file['name'];
                break;
            }
        }
    }
    
    if ($folderId) {
        echo "Listando archivos de la carpeta: {$folderName}\n\n";
        
        $folderFiles = $provider->getFiles($folderId);
        
        if ($folderFiles && !empty($folderFiles)) {
            echo "Archivos en la carpeta: " . count($folderFiles) . "\n\n";
            
            foreach ($folderFiles as $file) {
                $type = $file['type'] === 'file' ? '📄' : '📁';
                $name = $file['name'];
                $size = '';
                
                if ($file['type'] === 'file' && isset($file['size'])) {
                    $size = ' (' . formatBytes($file['size']) . ')';
                }
                
                echo "{$type} {$name}{$size}\n";
                
                if (isset($file['modified_at'])) {
                    echo "   Modificado: {$file['modified_at']}\n";
                }
                
                echo "\n";
            }
        } else {
            echo "La carpeta está vacía o no se pudo acceder.\n";
        }
    } else {
        echo "No se encontraron carpetas para explorar.\n";
    }

    // === EJEMPLO 5: Estadísticas generales ===
    echo "\n=== ESTADÍSTICAS GENERALES ===\n";
    
    if ($files) {
        $stats = [
            'total_files' => 0,
            'total_folders' => 0,
            'total_size' => 0,
            'file_types' => [],
            'largest_file' => null,
            'newest_file' => null,
        ];
        
        foreach ($files as $file) {
            if ($file['type'] === 'file') {
                $stats['total_files']++;
                
                if (isset($file['size'])) {
                    $stats['total_size'] += $file['size'];
                    
                    if (!$stats['largest_file'] || $file['size'] > $stats['largest_file']['size']) {
                        $stats['largest_file'] = $file;
                    }
                }
                
                // Determinar tipo de archivo por extensión
                $extension = pathinfo($file['name'], PATHINFO_EXTENSION);
                $extension = strtolower($extension);
                
                if (!isset($stats['file_types'][$extension])) {
                    $stats['file_types'][$extension] = 0;
                }
                $stats['file_types'][$extension]++;
                
                // Archivo más reciente
                if (isset($file['created_at'])) {
                    if (!$stats['newest_file'] || $file['created_at'] > $stats['newest_file']['created_at']) {
                        $stats['newest_file'] = $file;
                    }
                }
                
            } else {
                $stats['total_folders']++;
            }
        }
        
        echo "Total de archivos: " . $stats['total_files'] . "\n";
        echo "Total de carpetas: " . $stats['total_folders'] . "\n";
        echo "Tamaño total: " . formatBytes($stats['total_size']) . "\n";
        
        if ($stats['largest_file']) {
            echo "Archivo más grande: " . $stats['largest_file']['name'] . " (" . formatBytes($stats['largest_file']['size']) . ")\n";
        }
        
        if ($stats['newest_file']) {
            echo "Archivo más reciente: " . $stats['newest_file']['name'] . "\n";
        }
        
        if (!empty($stats['file_types'])) {
            echo "\nTipos de archivo:\n";
            arsort($stats['file_types']);
            foreach ($stats['file_types'] as $ext => $count) {
                $extDisplay = $ext ?: 'sin extensión';
                echo "- .{$extDisplay}: {$count} archivo(s)\n";
            }
        }
    }

} catch (Exception $e) {
    echo "Error al usar Mega: " . $e->getMessage() . "\n";
    echo "Detalles: " . $e->getTraceAsString() . "\n";
}

/**
 * Función auxiliar para formatear bytes
 */
function formatBytes($bytes, $precision = 2) {
    $units = array('B', 'KB', 'MB', 'GB', 'TB');
    
    for ($i = 0; $bytes > 1024 && $i < count($units) - 1; $i++) {
        $bytes /= 1024;
    }
    
    return round($bytes, $precision) . ' ' . $units[$i];
}

echo "\n=== EJEMPLO COMPLETADO ===\n";
echo "Este ejemplo muestra el uso de los métodos específicos del MegaProvider.\n";
echo "Para otros providers, estos métodos no están disponibles y debes usar\n";
echo "directamente las APIs correspondientes como se muestra en los otros ejemplos.\n";
