<?php

namespace LBC<PERSON>ev\OAuthManager\Providers;

use LBC<PERSON>ev\OAuthManager\Models\OAuthService;
use LBCDev\OAuthManager\Contracts\OAuthProviderInterface;

abstract class BaseOAuthProvider implements OAuthProviderInterface
{
    protected OAuthService $service;
    protected array $config;

    public function __construct(OAuthService $service)
    {
        $this->service = $service;
        $this->config = config("oauth-manager.services.{$service->service_type}") ?? [];
    }

    protected function getRedirectUri(): string
    {
        $callbackUrl = route('oauth-manager.callback', ['service' => $this->service->slug]);

        // Verificar si debe usar fwd.host para desarrollo local
        $useFwdService = $this->config['fields']['use_fwd_service_for_local_oauth'] ?? false;

        if ($useFwdService) {
            // Convertir la URL local a formato fwd.host
            // De: https://dominio.test/oauth-manager/callback/servicio
            // A: https://fwd.host/https://dominio.test/oauth-manager/callback/servicio
            return 'https://fwd.host/' . $callbackUrl;
        }

        return $callbackUrl;
    }

    public function setProvider(OAuthService $service)
    {
        $this->service = $service;
        $this->config = config("oauth-manager.services.{$service->service_type}") ?? [];
        return $this;
    }

    public function checkOAuthCredentials(): bool
    {
        $rawCredentials = $this->service->credentials;

        // Convertir a array si es string JSON
        if (is_string($rawCredentials)) {
            $credentials = json_decode($rawCredentials, true) ?: [];
        } else {
            $credentials = (array)$rawCredentials;
        }

        $clientId = $credentials['client_id'] ?? null;
        $clientSecret = $credentials['client_secret'] ?? null;

        if (!$clientId || !$clientSecret) {
            return false;
        }

        return true;
    }

    abstract public function getAuthorizationUrl(): string;
    abstract public function handleCallback(string $code): array;
    abstract public function refreshToken(): ?array;
    abstract public function revokeToken(): bool;
    // abstract public function testConnection(): bool;
}
