<?php

/**
 * Ejemplo de uso del DropboxProvider
 * 
 * Este archivo muestra cómo usar el DropboxProvider para:
 * - Crear un servicio OAuth de Dropbox
 * - Autorizar el servicio
 * - Obtener tokens válidos
 * - Usar la API de Dropbox
 */

require_once __DIR__ . '/../vendor/autoload.php';

use LBCDev\OAuthManager\Models\OAuthService;

// 1. Crear un servicio OAuth de Dropbox
$dropboxService = OAuthService::create([
    'name' => 'Mi cuenta de Dropbox',
    'service_type' => 'dropbox',
    'slug' => 'mi-dropbox',
    'credentials' => [
        'client_id' => 'tu-app-key',
        'client_secret' => 'tu-app-secret',
    ],
    'is_active' => true,
]);

echo "Servicio de Dropbox creado con ID: " . $dropboxService->id . "\n";

// 2. Obtener URL de autorización
$provider = $dropboxService->getProviderInstance();
$authUrl = $provider->getAuthorizationUrl();

echo "URL de autorización: " . $authUrl . "\n";
echo "Visita esta URL para autorizar la aplicación.\n";

// 3. Después de la autorización, simular que ya tenemos un token
// (en la práctica, esto vendría del callback OAuth)
$dropboxService->update([
    'access_token' => 'token_de_ejemplo',
    'refresh_token' => 'refresh_token_de_ejemplo',
    'expires_at' => now()->addHours(4),
]);

// 4. Obtener token válido usando el modelo directamente
if ($dropboxService->ensureValidToken()) {
    echo "Token válido obtenido: " . substr($dropboxService->access_token, 0, 20) . "...\n";

    // 5. Usar el provider para operaciones básicas
    try {
        // Solo métodos básicos disponibles: getAuthorizationUrl, handleCallback, refreshToken, revokeToken
        echo "Provider configurado correctamente.\n";
        echo "Para operaciones avanzadas con Dropbox, usa la API directamente.\n";
        echo "Ver ejemplo completo en: docs/examples/dropbox-usage-api.php\n";
    } catch (Exception $e) {
        echo "Error al usar el provider de Dropbox: " . $e->getMessage() . "\n";
    }
} else {
    echo "No se pudo obtener un token válido.\n";
}

// 6. Revocar token (opcional)
// $success = $dropboxService->revokeToken();
// echo "Token revocado: " . ($success ? 'SÍ' : 'NO') . "\n";

echo "\nEjemplo completado.\n";
