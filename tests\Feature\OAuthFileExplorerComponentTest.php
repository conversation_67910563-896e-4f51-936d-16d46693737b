<?php

namespace LBCDev\OAuthFileExplorer\Tests\Feature;

use LBCDev\OAuthFileExplorer\Tests\TestCase;
use LBCDev\OAuthFileExplorer\Livewire\OAuthFileExplorer;
use LBCDev\OAuthManager\Models\OAuthService;
use Livewire\Livewire;
use PHPUnit\Framework\Attributes\Test;

class OAuthFileExplorerComponentTest extends TestCase
{
    public function test_it_can_mount_without_oauth_service()
    {
        Livewire::test(OAuthFileExplorer::class)
            ->assertSet('oauthServiceId', null)
            ->assertSet('files', [])
            ->assertSet('error', null)
            ->assertSee('Please select a service first to browse files');
    }

    public function test_it_can_mount_with_oauth_service_id()
    {
        $oauthService = OAuthService::create([
            'name' => 'Test Google Drive',
            'service_type' => 'google_drive',
            'access_token' => 'test_token',
            'is_active' => true,
        ]);

        Livewire::test(OAuthFileExplorer::class, [
            'oauthServiceId' => $oauthService->id
        ])
            ->assertSet('oauthServiceId', $oauthService->id)
            ->assertSet('serviceType', 'google_drive');
    }

    public function test_it_shows_error_when_oauth_service_not_found()
    {
        Livewire::test(OAuthFileExplorer::class, [
            'oauthServiceId' => 999
        ])
            ->assertSet('error', 'Error initializing explorer: OAuth service not found');
    }

    public function test_it_can_set_field_name()
    {
        Livewire::test(OAuthFileExplorer::class, [
            'fieldName' => 'custom_field'
        ])
            ->assertSet('fieldName', 'custom_field');
    }

    public function test_it_can_set_accepted_mime_types()
    {
        $acceptedTypes = ['application/pdf', 'image/jpeg'];

        Livewire::test(OAuthFileExplorer::class, [
            'acceptedMimeTypes' => $acceptedTypes
        ])
            ->assertSet('acceptedMimeTypes', $acceptedTypes);
    }

    public function test_it_rejects_file_with_unaccepted_mime_type()
    {
        $acceptedTypes = ['application/pdf'];

        Livewire::test(OAuthFileExplorer::class, [
            'acceptedMimeTypes' => $acceptedTypes
        ])
            ->call('selectFile', 'file123', 'test.jpg', 'http://example.com/test.jpg', 'image/jpeg')
            ->assertSet('error', "File type 'image/jpeg' is not accepted.")
            ->assertSet('selectedFile', null);
    }

    public function test_it_accepts_file_with_accepted_mime_type()
    {
        $acceptedTypes = ['image/jpeg'];

        Livewire::test(OAuthFileExplorer::class, [
            'acceptedMimeTypes' => $acceptedTypes
        ])
            ->call('selectFile', 'file123', 'test.jpg', 'http://example.com/test.jpg', 'image/jpeg')
            ->assertSet('error', null)
            ->assertSet('selectedFile', [
                'id' => 'file123',
                'name' => 'test.jpg',
                'url' => 'http://example.com/test.jpg',
                'mimeType' => 'image/jpeg'
            ])
            ->assertSet('selectedFileUrl', 'http://example.com/test.jpg');
    }

    public function test_it_can_open_folder()
    {
        Livewire::test(OAuthFileExplorer::class)
            ->call('openFolder', 'folder123')
            ->assertSet('currentFolderId', 'folder123')
            ->assertSet('searchQuery', '');
    }

    public function test_it_can_clear_search()
    {
        Livewire::test(OAuthFileExplorer::class)
            ->set('searchQuery', 'test query')
            ->call('clearSearch')
            ->assertSet('searchQuery', '');
    }

    public function test_it_returns_correct_service_display_names()
    {
        $component = new OAuthFileExplorer();

        $component->serviceType = 'google_drive';
        $result = $component->getServiceTypeDisplayName();
        $this->assertTrue($result === 'Google Drive');

        $component->serviceType = 'dropbox';
        $result = $component->getServiceTypeDisplayName();
        $this->assertTrue($result === 'Dropbox');

        $component->serviceType = 'onedrive';
        $result = $component->getServiceTypeDisplayName();
        $this->assertTrue($result === 'OneDrive');
    }

    public function test_it_handles_search_with_empty_query()
    {
        Livewire::test(OAuthFileExplorer::class)
            ->set('searchQuery', '')
            ->call('search')
            ->assertSet('searchQuery', '');
    }

    public function test_it_shows_loading_state()
    {
        Livewire::test(OAuthFileExplorer::class)
            ->set('isLoading', true)
            ->assertSee('Loading...');
    }

    public function test_it_shows_error_messages()
    {
        Livewire::test(OAuthFileExplorer::class)
            ->set('error', 'Test error message')
            ->assertSee('Test error message');
    }

    public function test_it_shows_selected_file_info()
    {
        Livewire::test(OAuthFileExplorer::class)
            ->set('selectedFile', [
                'id' => 'file123',
                'name' => 'test.pdf',
                'url' => 'http://example.com/test.pdf',
                'mimeType' => 'application/pdf'
            ])
            ->set('selectedFileUrl', 'http://example.com/test.pdf')
            ->assertSee('Selected: test.pdf')
            ->assertSee('http://example.com/test.pdf');
    }

    public function test_it_shows_service_type_display()
    {
        Livewire::test(OAuthFileExplorer::class)
            ->set('oauthServiceId', 1)
            ->set('serviceType', 'google_drive')
            ->assertSee('Browsing Google Drive');
    }

    public function test_it_shows_no_files_message_when_empty()
    {
        Livewire::test(OAuthFileExplorer::class)
            ->set('oauthServiceId', 1)
            ->set('files', [])
            ->set('isLoading', false)
            ->assertSee('No files found')
            ->assertSee('This folder is empty');
    }

    public function test_it_shows_no_search_results_message()
    {
        Livewire::test(OAuthFileExplorer::class)
            ->set('oauthServiceId', 1)
            ->set('files', [])
            ->set('searchQuery', 'test query')
            ->set('isLoading', false)
            ->assertSee('No files found')
            ->assertSee('No files match your search criteria');
    }
}
