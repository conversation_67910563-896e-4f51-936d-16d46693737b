<?php // tests\Unit\Providers\GoogleDriveProviderTest.php

namespace LBCDev\OAuthManager\Tests\Unit\Providers;

use LBCDev\OAuthManager\Tests\TestCase;
use LBCDev\OAuthManager\Models\OAuthService;
use L<PERSON><PERSON>ev\OAuthManager\Providers\GoogleDriveProvider;
use League\OAuth2\Client\Provider\Google;
use League\OAuth2\Client\Token\AccessToken;
use Mockery;

class GoogleDriveProviderTest extends TestCase
{
    private GoogleDriveProvider $provider;
    private OAuthService $service;

    protected function setUp(): void
    {
        parent::setUp();

        $this->service = new OAuthService([
            'service_type' => 'google_drive',
            'name' => 'Test Google Drive',
            'slug' => 'test-google-drive',
            'credentials' => [
                'client_id' => 'test_client_id',
                'client_secret' => 'test_client_secret',
            ],
            'access_token' => 'test_access_token',
            'refresh_token' => 'test_refresh_token',
        ]);

        $this->provider = new GoogleDriveProvider($this->service);
    }

    public function test_get_authorization_url_returns_valid_url()
    {
        $url = $this->provider->getAuthorizationUrl();

        $this->assertStringContainsString('https://accounts.google.com/o/oauth2/', $url);
        $this->assertStringContainsString('client_id=test_client_id', $url);
        $this->assertStringContainsString('access_type=offline', $url);
    }

    public function test_handle_callback_success()
    {
        $mockProvider = Mockery::mock(Google::class);
        $mockToken = Mockery::mock(AccessToken::class);

        $mockToken->shouldReceive('getToken')->andReturn('new_access_token');
        $mockToken->shouldReceive('getRefreshToken')->andReturn('new_refresh_token');
        $mockToken->shouldReceive('getExpires')->andReturn(time() + 3600);

        $mockProvider->shouldReceive('getAccessToken')
            ->with('authorization_code', ['code' => 'test_code'])
            ->andReturn($mockToken);

        $fakeProvider = new FakeGoogleDriveProvider($this->service);
        $fakeProvider->setMockProvider($mockProvider);

        $result = $fakeProvider->handleCallback('test_code');

        $this->assertArrayHasKey('access_token', $result);
        $this->assertArrayHasKey('refresh_token', $result);
        $this->assertArrayHasKey('expires_at', $result);
    }

    public function test_refresh_token_success()
    {
        $mockProvider = Mockery::mock(Google::class);
        $mockToken = Mockery::mock(AccessToken::class);

        $mockToken->shouldReceive('getToken')->andReturn('refreshed_access_token');
        $mockToken->shouldReceive('getRefreshToken')->andReturn('refreshed_refresh_token');
        $mockToken->shouldReceive('getExpires')->andReturn(time() + 3600);

        $mockProvider->shouldReceive('getAccessToken')
            ->with('refresh_token', ['refresh_token' => 'test_refresh_token'])
            ->andReturn($mockToken);

        $provider = new FakeGoogleDriveProvider($this->service);
        $provider->setMockProvider($mockProvider);

        $result = $provider->refreshToken();

        $this->assertIsArray($result);
        $this->assertEquals('refreshed_access_token', $result['access_token']);
        $this->assertEquals('refreshed_refresh_token', $result['refresh_token']);
    }


    public function test_refresh_token_failure()
    {
        $mockProvider = Mockery::mock(Google::class);
        $mockProvider->shouldReceive('getAccessToken')
            ->andThrow(new \Exception('Token refresh failed'));

        $provider = new FakeGoogleDriveProvider($this->service);
        $provider->setMockProvider($mockProvider);

        $result = $provider->refreshToken();

        $this->assertNull($result);
    }


    // public function test_test_connection_success()
    // {
    //     $mockClient = Mockery::mock(\Google_Client::class);
    //     $mockDriveService = Mockery::mock(\Google\Service\Drive::class);
    //     $mockAbout = Mockery::mock(\Google\Service\Drive\About::class);
    //     $mockUser = Mockery::mock(\Google\Service\Drive\User::class);

    //     $mockAbout->shouldReceive('getUser')->andReturn($mockUser);
    //     $mockDriveService->about = Mockery::mock();
    //     $mockDriveService->about->shouldReceive('get')->andReturn($mockAbout);

    //     // Expect setAccessToken to be called
    //     $mockClient->shouldReceive('setAccessToken')->once()->with('test_access_token');

    //     $fakeProvider = new FakeGoogleDriveProvider($this->service);
    //     $fakeProvider->setMockClient($mockClient);
    //     $fakeProvider->setMockDriveService($mockDriveService);

    //     $this->assertTrue($fakeProvider->testConnection());
    // }

    // public function test_test_connection_failure()
    // {
    //     $this->service->access_token = null;

    //     $this->assertFalse($this->provider->testConnection());
    // }

    protected function tearDown(): void
    {
        Mockery::close();
        parent::tearDown();
    }
}


class FakeGoogleDriveProvider extends GoogleDriveProvider
{
    protected Google $mockProvider;
    protected $mockClient;
    protected $mockDriveService;

    public function setMockProvider(Google $provider)
    {
        $this->mockProvider = $provider;
    }

    public function setMockClient($client)
    {
        $this->mockClient = $client;
    }

    public function setMockDriveService($driveService)
    {
        $this->mockDriveService = $driveService;
    }

    protected function getProvider(): Google
    {
        return $this->mockProvider ?? parent::getProvider();
    }

    // Override getClient to return our mock client
    public function getClient(): \Google_Client
    {
        $this->mockClient->setAccessToken($this->service->access_token);
        return $this->mockClient;
    }

    // Override createDriveService to return our mock service
    protected function createDriveService(\Google_Client $client): \Google\Service\Drive
    {
        return $this->mockDriveService;
    }
}
