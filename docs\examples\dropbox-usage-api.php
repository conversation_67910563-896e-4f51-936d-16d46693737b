<?php

/**
 * Ejemplo de uso con Dropbox API
 * 
 * Este archivo muestra cómo usar el token de acceso de Dropbox
 * para interactuar directamente con la API de Dropbox.
 */

require_once __DIR__ . '/../../vendor/autoload.php';

use GuzzleHttp\Client;
use LBCDev\OAuthManager\Models\OAuthService;

// Obtener el servicio de Dropbox
$dropboxService = OAuthService::where('service_type', 'dropbox')
    ->where('is_active', true)
    ->first();

if (!$dropboxService) {
    echo "No se encontró ningún servicio de Dropbox activo.\n";
    exit(1);
}

// Asegurar que tenemos un token válido
if (!$dropboxService->ensureValidToken()) {
    echo "No se pudo obtener un token válido para Dropbox.\n";
    exit(1);
}

echo "Token válido obtenido para Dropbox.\n";

try {
    // Crear cliente HTTP
    $client = new Client();
    
    // Headers comunes para todas las peticiones
    $headers = [
        'Authorization' => 'Bearer ' . $dropboxService->access_token,
        'Content-Type' => 'application/json',
    ];

    // === EJEMPLO 1: Obtener información del usuario ===
    echo "\n=== INFORMACIÓN DEL USUARIO ===\n";
    
    $response = $client->post('https://api.dropboxapi.com/2/users/get_current_account', [
        'headers' => $headers,
    ]);

    $userInfo = json_decode($response->getBody()->getContents(), true);
    
    echo "Nombre: " . $userInfo['name']['display_name'] . "\n";
    echo "Email: " . $userInfo['email'] . "\n";
    echo "ID de cuenta: " . $userInfo['account_id'] . "\n";
    echo "País: " . ($userInfo['country'] ?? 'N/A') . "\n";

    // === EJEMPLO 2: Obtener información de espacio ===
    echo "\n=== INFORMACIÓN DE ESPACIO ===\n";
    
    $response = $client->post('https://api.dropboxapi.com/2/users/get_space_usage', [
        'headers' => $headers,
    ]);

    $spaceInfo = json_decode($response->getBody()->getContents(), true);
    
    echo "Espacio usado: " . formatBytes($spaceInfo['used']) . "\n";
    
    if (isset($spaceInfo['allocation']['allocated'])) {
        echo "Espacio total: " . formatBytes($spaceInfo['allocation']['allocated']) . "\n";
        $remaining = $spaceInfo['allocation']['allocated'] - $spaceInfo['used'];
        echo "Espacio disponible: " . formatBytes($remaining) . "\n";
    }

    // === EJEMPLO 3: Listar archivos de la raíz ===
    echo "\n=== ARCHIVOS EN LA RAÍZ ===\n";
    
    $response = $client->post('https://api.dropboxapi.com/2/files/list_folder', [
        'headers' => $headers,
        'json' => [
            'path' => '', // Carpeta raíz
            'recursive' => false,
            'include_media_info' => false,
            'include_deleted' => false,
            'include_has_explicit_shared_members' => false,
        ],
    ]);

    $files = json_decode($response->getBody()->getContents(), true);
    
    if (empty($files['entries'])) {
        echo "No se encontraron archivos en la raíz.\n";
    } else {
        echo "Archivos encontrados: " . count($files['entries']) . "\n\n";
        
        foreach ($files['entries'] as $file) {
            $type = $file['.tag'] === 'folder' ? '📁 Carpeta' : '📄 Archivo';
            $size = isset($file['size']) ? ' (' . formatBytes($file['size']) . ')' : '';
            
            echo "{$type}: {$file['name']}{$size}\n";
            echo "  Ruta: {$file['path_lower']}\n";
            
            if (isset($file['client_modified'])) {
                echo "  Modificado: {$file['client_modified']}\n";
            }
            if (isset($file['server_modified'])) {
                echo "  Servidor: {$file['server_modified']}\n";
            }
            echo "\n";
        }
    }

    // === EJEMPLO 4: Subir un archivo ===
    echo "\n=== SUBIR ARCHIVO ===\n";
    
    $fileName = '/documento-dropbox-ejemplo.txt';
    $fileContent = "Este es un archivo de ejemplo para Dropbox.\n";
    $fileContent .= "Creado desde Laravel OAuth Manager.\n";
    $fileContent .= "Fecha: " . date('Y-m-d H:i:s') . "\n";

    $response = $client->post('https://content.dropboxapi.com/2/files/upload', [
        'headers' => [
            'Authorization' => 'Bearer ' . $dropboxService->access_token,
            'Content-Type' => 'application/octet-stream',
            'Dropbox-API-Arg' => json_encode([
                'path' => $fileName,
                'mode' => 'add',
                'autorename' => true,
                'mute' => false,
            ]),
        ],
        'body' => $fileContent,
    ]);

    $uploadedFile = json_decode($response->getBody()->getContents(), true);
    
    echo "Archivo subido exitosamente!\n";
    echo "Nombre: " . $uploadedFile['name'] . "\n";
    echo "Ruta: " . $uploadedFile['path_display'] . "\n";
    echo "ID: " . $uploadedFile['id'] . "\n";
    echo "Tamaño: " . formatBytes($uploadedFile['size']) . "\n";

    // === EJEMPLO 5: Crear una carpeta ===
    echo "\n=== CREAR CARPETA ===\n";
    
    $folderPath = '/Mi Carpeta Laravel';
    
    $response = $client->post('https://api.dropboxapi.com/2/files/create_folder_v2', [
        'headers' => $headers,
        'json' => [
            'path' => $folderPath,
            'autorename' => true,
        ],
    ]);

    $createdFolder = json_decode($response->getBody()->getContents(), true);
    
    echo "Carpeta creada exitosamente!\n";
    echo "Nombre: " . $createdFolder['metadata']['name'] . "\n";
    echo "Ruta: " . $createdFolder['metadata']['path_display'] . "\n";
    echo "ID: " . $createdFolder['metadata']['id'] . "\n";

    // === EJEMPLO 6: Buscar archivos ===
    echo "\n=== BUSCAR ARCHIVOS ===\n";
    
    $searchQuery = 'documento';
    $response = $client->post('https://api.dropboxapi.com/2/files/search_v2', [
        'headers' => $headers,
        'json' => [
            'query' => $searchQuery,
            'options' => [
                'path' => '',
                'max_results' => 10,
                'file_status' => 'active',
                'filename_only' => false,
            ],
        ],
    ]);

    $searchResults = json_decode($response->getBody()->getContents(), true);
    
    if (empty($searchResults['matches'])) {
        echo "No se encontraron archivos con el término '{$searchQuery}'.\n";
    } else {
        echo "Archivos encontrados con '{$searchQuery}': " . count($searchResults['matches']) . "\n\n";
        
        foreach ($searchResults['matches'] as $match) {
            $file = $match['metadata']['metadata'];
            $type = $file['.tag'] === 'folder' ? '📁' : '📄';
            $size = isset($file['size']) ? ' (' . formatBytes($file['size']) . ')' : '';
            
            echo "{$type} {$file['name']}{$size}\n";
            echo "  Ruta: {$file['path_display']}\n\n";
        }
    }

    // === EJEMPLO 7: Obtener enlace de descarga ===
    echo "\n=== ENLACE DE DESCARGA ===\n";
    
    if (isset($uploadedFile)) {
        $response = $client->post('https://api.dropboxapi.com/2/files/get_temporary_link', [
            'headers' => $headers,
            'json' => [
                'path' => $uploadedFile['path_lower'],
            ],
        ]);

        $linkInfo = json_decode($response->getBody()->getContents(), true);
        
        echo "Enlace temporal de descarga generado:\n";
        echo "URL: " . $linkInfo['link'] . "\n";
        echo "Expira: Este enlace es temporal y expirará en unas horas.\n";
    }

    // === EJEMPLO 8: Descargar un archivo ===
    echo "\n=== DESCARGAR ARCHIVO ===\n";
    
    if (isset($uploadedFile)) {
        $response = $client->post('https://content.dropboxapi.com/2/files/download', [
            'headers' => [
                'Authorization' => 'Bearer ' . $dropboxService->access_token,
                'Dropbox-API-Arg' => json_encode([
                    'path' => $uploadedFile['path_lower'],
                ]),
            ],
        ]);

        $downloadedContent = $response->getBody()->getContents();
        
        echo "Contenido del archivo descargado:\n";
        echo "---\n";
        echo $downloadedContent;
        echo "---\n";
    }

} catch (Exception $e) {
    echo "Error al usar la API de Dropbox: " . $e->getMessage() . "\n";
    
    // Si es un error HTTP, mostrar más detalles
    if (method_exists($e, 'getResponse') && $e->getResponse()) {
        $response = $e->getResponse();
        echo "Código de estado: " . $response->getStatusCode() . "\n";
        echo "Respuesta: " . $response->getBody()->getContents() . "\n";
    }
}

/**
 * Función auxiliar para formatear bytes
 */
function formatBytes($bytes, $precision = 2) {
    $units = array('B', 'KB', 'MB', 'GB', 'TB');
    
    for ($i = 0; $bytes > 1024 && $i < count($units) - 1; $i++) {
        $bytes /= 1024;
    }
    
    return round($bytes, $precision) . ' ' . $units[$i];
}

echo "\n=== EJEMPLO COMPLETADO ===\n";
echo "Para más información sobre la API de Dropbox, visita:\n";
echo "https://www.dropbox.com/developers/documentation/http/documentation\n";
