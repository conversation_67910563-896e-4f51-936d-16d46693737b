# Uso del comando oauth:show

Este documento muestra ejemplos de uso del comando `oauth:show` para inspeccionar servicios OAuth.

## Mostrar todos los servicios

```bash
php artisan oauth:show
```

**Salida de ejemplo:**

```
📋 Lista de servicios OAuth (3 servicios)

🔗 Google Drive Principal
   Slug: google-drive-principal
   Tipo: google_drive
   Estado: ✅ Activo
   Token: ✅ Válido
   Access Token: ya29.a0Ae...XYZ123456
   Refresh Token: 1//04Abc...DEF789012
   Expira: 2025-07-28 11:30:45
   Último uso: 2025-07-28 09:15:22
   Creado: 2025-07-20 14:30:00
   Actualizado: 2025-07-28 09:15:22

🔗 Dropbox Backup
   Slug: dropbox-backup
   Tipo: dropbox
   Estado: ❌ Inactivo
   Token: ⏰ Expirado (puede refrescarse)
   Access Token: sl.B1abc...XYZ789012
   Refresh Token: abc123...DEF456789
   Expira: 2025-07-27 08:45:30
   Último uso: 2025-07-26 16:20:10
   Creado: 2025-07-15 10:00:00
   Actualizado: 2025-07-26 16:20:10

🔗 YouTube Channel
   Slug: youtube-channel
   Tipo: youtube
   Estado: ✅ Activo
   Token: ❌ No autorizado
   Access Token: No disponible
   Refresh Token: No disponible
   Expira: Sin expiración
   Último uso: Nunca
   Creado: 2025-07-28 08:00:00
   Actualizado: 2025-07-28 08:00:00
```

## Mostrar un servicio específico

```bash
php artisan oauth:show google-drive-principal
```

**Salida de ejemplo:**

```
📋 Información del servicio OAuth

🔗 Google Drive Principal
   Slug: google-drive-principal
   Tipo: google_drive
   Estado: ✅ Activo
   Token: ✅ Válido
   Access Token: ya29.a0Ae...XYZ123456
   Refresh Token: 1//04Abc...DEF789012
   Expira: 2025-07-28 11:30:45
   Último uso: 2025-07-28 09:15:22
   Creado: 2025-07-20 14:30:00
   Actualizado: 2025-07-28 09:15:22
```

## Mostrar tokens completos

⚠️ **ADVERTENCIA**: Esta opción muestra los tokens completos sin truncar. Úsala solo en entornos seguros.

```bash
# Mostrar todos los servicios con tokens completos
php artisan oauth:show --full-tokens

# Mostrar un servicio específico con tokens completos
php artisan oauth:show google-drive-principal --full-tokens
```

**Salida de ejemplo con --full-tokens:**

```
📋 Información del servicio OAuth

🔗 Google Drive Principal
   Slug: google-drive-principal
   Tipo: google_drive
   Estado: ✅ Activo
   Token: ✅ Válido
   Access Token: ya29.a0AeB1_xYz123456789AbCdEfGhIjKlMnOpQrStUvWxYz987654321
   Refresh Token: 1//04AbCdEfGhIjKlMnOpQrStUvWxYz123456789_AbCdEfGhIjKlMnOp
   Expira: 2025-07-28 11:30:45
   Último uso: 2025-07-28 09:15:22
   Creado: 2025-07-20 14:30:00
   Actualizado: 2025-07-28 09:15:22
```

## Casos de uso comunes

### 1. Verificar estado de todos los servicios

```bash
php artisan oauth:show
```

Útil para obtener una vista general del estado de todos los servicios OAuth configurados.

### 2. Inspeccionar un servicio específico

```bash
php artisan oauth:show mi-servicio-slug
```

Útil para obtener información detallada de un servicio específico durante desarrollo o debugging.

### 3. Verificar tokens antes de usar un servicio

```bash
php artisan oauth:show mi-servicio-slug
```

Permite verificar si un token está válido, expirado o necesita autorización antes de intentar usar el servicio.

### 4. Obtener tokens completos para debugging

```bash
php artisan oauth:show mi-servicio-slug --full-tokens
```

⚠️ **Solo en entornos de desarrollo**: Útil para debugging cuando necesitas ver el token completo para verificar su formato o copiarlo para pruebas manuales.

## Estados de token

-  **✅ Válido**: El token está activo y puede usarse
-  **⏰ Expirado (puede refrescarse)**: El token expiró pero hay refresh token disponible
-  **⏰ Expirado (sin refresh token)**: El token expiró y no puede refrescarse automáticamente
-  **❌ No autorizado**: El servicio no tiene tokens configurados

## Seguridad

Los tokens se muestran truncados por seguridad:

-  Tokens cortos (≤20 caracteres): se muestran completos
-  Tokens largos: se muestran los primeros 10 y últimos 10 caracteres con "..." en el medio

Ejemplo: `ya29.a0AeB1...XYZ123456`

## Integración con otros comandos

Este comando es útil en combinación con otros comandos del paquete:

```bash
# 1. Verificar estado
php artisan oauth:show mi-servicio

# 2. Si el token está expirado, refrescarlo
php artisan oauth:refresh mi-servicio-id

# 3. Verificar que se refrescó correctamente
php artisan oauth:show mi-servicio

# 4. Probar la conexión
php artisan oauth:test mi-servicio-id
```
