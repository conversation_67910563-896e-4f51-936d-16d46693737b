<?php

/**
 * Ejemplo de uso con OneDrive
 * 
 * Este archivo muestra cómo usar el token de acceso de OneDrive
 * para interactuar directamente con la API de Microsoft Graph.
 */

require_once __DIR__ . '/../../vendor/autoload.php';

use GuzzleHttp\Client;
use LBCDev\OAuthManager\Models\OAuthService;

// Obtener el servicio de OneDrive
$onedriveService = OAuthService::where('service_type', 'onedrive')
    ->where('is_active', true)
    ->first();

if (!$onedriveService) {
    echo "No se encontró ningún servicio de OneDrive activo.\n";
    exit(1);
}

// Asegurar que tenemos un token válido
if (!$onedriveService->ensureValidToken()) {
    echo "No se pudo obtener un token válido para OneDrive.\n";
    exit(1);
}

echo "Token válido obtenido para OneDrive.\n";

try {
    // Crear cliente HTTP
    $client = new Client();
    
    // Headers comunes para todas las peticiones
    $headers = [
        'Authorization' => 'Bearer ' . $onedriveService->access_token,
        'Content-Type' => 'application/json',
    ];

    // === EJEMPLO 1: Obtener información del usuario ===
    echo "\n=== INFORMACIÓN DEL USUARIO ===\n";
    
    $response = $client->get('https://graph.microsoft.com/v1.0/me', [
        'headers' => $headers,
    ]);

    $userInfo = json_decode($response->getBody()->getContents(), true);
    
    echo "Usuario: " . $userInfo['displayName'] . "\n";
    echo "Email: " . $userInfo['mail'] ?? $userInfo['userPrincipalName'] . "\n";
    echo "ID: " . $userInfo['id'] . "\n";

    // === EJEMPLO 2: Obtener información de OneDrive ===
    echo "\n=== INFORMACIÓN DE ONEDRIVE ===\n";
    
    $response = $client->get('https://graph.microsoft.com/v1.0/me/drive', [
        'headers' => $headers,
    ]);

    $driveInfo = json_decode($response->getBody()->getContents(), true);
    
    echo "Tipo de Drive: " . $driveInfo['driveType'] . "\n";
    echo "ID del Drive: " . $driveInfo['id'] . "\n";
    
    if (isset($driveInfo['quota'])) {
        $quota = $driveInfo['quota'];
        echo "Espacio total: " . formatBytes($quota['total']) . "\n";
        echo "Espacio usado: " . formatBytes($quota['used']) . "\n";
        echo "Espacio disponible: " . formatBytes($quota['remaining']) . "\n";
    }

    // === EJEMPLO 3: Listar archivos de la raíz ===
    echo "\n=== ARCHIVOS EN LA RAÍZ ===\n";
    
    $response = $client->get('https://graph.microsoft.com/v1.0/me/drive/root/children', [
        'headers' => $headers,
    ]);

    $files = json_decode($response->getBody()->getContents(), true);
    
    if (empty($files['value'])) {
        echo "No se encontraron archivos en la raíz.\n";
    } else {
        echo "Archivos encontrados: " . count($files['value']) . "\n\n";
        
        foreach ($files['value'] as $file) {
            $type = isset($file['folder']) ? '📁 Carpeta' : '📄 Archivo';
            $size = isset($file['size']) ? ' (' . formatBytes($file['size']) . ')' : '';
            
            echo "{$type}: {$file['name']}{$size}\n";
            echo "  ID: {$file['id']}\n";
            echo "  Creado: {$file['createdDateTime']}\n";
            echo "  Modificado: {$file['lastModifiedDateTime']}\n\n";
        }
    }

    // === EJEMPLO 4: Subir un archivo ===
    echo "\n=== SUBIR ARCHIVO ===\n";
    
    $fileName = 'documento-onedrive-ejemplo.txt';
    $fileContent = "Este es un archivo de ejemplo para OneDrive.\n";
    $fileContent .= "Creado desde Laravel OAuth Manager.\n";
    $fileContent .= "Fecha: " . date('Y-m-d H:i:s') . "\n";

    $response = $client->put("https://graph.microsoft.com/v1.0/me/drive/root:/{$fileName}:/content", [
        'headers' => [
            'Authorization' => 'Bearer ' . $onedriveService->access_token,
            'Content-Type' => 'text/plain',
        ],
        'body' => $fileContent,
    ]);

    $uploadedFile = json_decode($response->getBody()->getContents(), true);
    
    echo "Archivo subido exitosamente!\n";
    echo "Nombre: " . $uploadedFile['name'] . "\n";
    echo "ID: " . $uploadedFile['id'] . "\n";
    echo "Tamaño: " . formatBytes($uploadedFile['size']) . "\n";
    echo "URL de descarga: " . $uploadedFile['@microsoft.graph.downloadUrl'] . "\n";

    // === EJEMPLO 5: Crear una carpeta ===
    echo "\n=== CREAR CARPETA ===\n";
    
    $folderData = [
        'name' => 'Mi Carpeta Laravel',
        'folder' => new stdClass(), // Indica que es una carpeta
        '@microsoft.graph.conflictBehavior' => 'rename' // Renombrar si ya existe
    ];

    $response = $client->post('https://graph.microsoft.com/v1.0/me/drive/root/children', [
        'headers' => $headers,
        'json' => $folderData,
    ]);

    $createdFolder = json_decode($response->getBody()->getContents(), true);
    
    echo "Carpeta creada exitosamente!\n";
    echo "Nombre: " . $createdFolder['name'] . "\n";
    echo "ID: " . $createdFolder['id'] . "\n";

    // === EJEMPLO 6: Buscar archivos ===
    echo "\n=== BUSCAR ARCHIVOS ===\n";
    
    $searchQuery = 'documento';
    $response = $client->get("https://graph.microsoft.com/v1.0/me/drive/root/search(q='{$searchQuery}')", [
        'headers' => $headers,
    ]);

    $searchResults = json_decode($response->getBody()->getContents(), true);
    
    if (empty($searchResults['value'])) {
        echo "No se encontraron archivos con el término '{$searchQuery}'.\n";
    } else {
        echo "Archivos encontrados con '{$searchQuery}': " . count($searchResults['value']) . "\n\n";
        
        foreach ($searchResults['value'] as $file) {
            $type = isset($file['folder']) ? '📁' : '📄';
            echo "{$type} {$file['name']}\n";
            echo "  Ruta: {$file['parentReference']['path']}\n";
            echo "  Tamaño: " . formatBytes($file['size']) . "\n\n";
        }
    }

    // === EJEMPLO 7: Descargar un archivo ===
    echo "\n=== DESCARGAR ARCHIVO ===\n";
    
    if (isset($uploadedFile)) {
        $response = $client->get("https://graph.microsoft.com/v1.0/me/drive/items/{$uploadedFile['id']}/content", [
            'headers' => [
                'Authorization' => 'Bearer ' . $onedriveService->access_token,
            ],
        ]);

        $downloadedContent = $response->getBody()->getContents();
        
        echo "Contenido del archivo descargado:\n";
        echo "---\n";
        echo $downloadedContent;
        echo "---\n";
    }

} catch (Exception $e) {
    echo "Error al usar la API de OneDrive: " . $e->getMessage() . "\n";
    
    // Si es un error HTTP, mostrar más detalles
    if (method_exists($e, 'getResponse') && $e->getResponse()) {
        $response = $e->getResponse();
        echo "Código de estado: " . $response->getStatusCode() . "\n";
        echo "Respuesta: " . $response->getBody()->getContents() . "\n";
    }
}

/**
 * Función auxiliar para formatear bytes
 */
function formatBytes($bytes, $precision = 2) {
    $units = array('B', 'KB', 'MB', 'GB', 'TB');
    
    for ($i = 0; $bytes > 1024 && $i < count($units) - 1; $i++) {
        $bytes /= 1024;
    }
    
    return round($bytes, $precision) . ' ' . $units[$i];
}

echo "\n=== EJEMPLO COMPLETADO ===\n";
echo "Para más información sobre la API de Microsoft Graph, visita:\n";
echo "https://docs.microsoft.com/en-us/graph/api/resources/onedrive\n";
