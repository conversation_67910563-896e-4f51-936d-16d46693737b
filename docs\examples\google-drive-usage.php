<?php

/**
 * Ejemplo de uso con Google Drive
 * 
 * Este archivo muestra cómo usar el token de acceso de Google Drive
 * para interactuar directamente con la API de Google Drive.
 */

require_once __DIR__ . '/../../vendor/autoload.php';

use Google_Client;
use Google_Service_Drive;
use Google_Service_Drive_DriveFile;
use LBCDev\OAuthManager\Models\OAuthService;

// Obtener el servicio de Google Drive
$googleService = OAuthService::where('service_type', 'google_drive')
    ->where('is_active', true)
    ->first();

if (!$googleService) {
    echo "No se encontró ningún servicio de Google Drive activo.\n";
    exit(1);
}

// Asegurar que tenemos un token válido
if (!$googleService->ensureValidToken()) {
    echo "No se pudo obtener un token válido para Google Drive.\n";
    exit(1);
}

echo "Token válido obtenido para Google Drive.\n";

try {
    // Configurar el cliente de Google
    $client = new Google_Client();
    $client->setAccessToken($googleService->access_token);

    // Crear el servicio de Drive
    $driveService = new Google_Service_Drive($client);

    // === EJEMPLO 1: Subir un archivo ===
    echo "\n=== SUBIR ARCHIVO ===\n";
    
    // Crear metadata del archivo
    $file = new Google_Service_Drive_DriveFile();
    $file->setName('documento-ejemplo.txt');
    $file->setDescription('Archivo de ejemplo subido desde Laravel OAuth Manager');

    // Contenido del archivo (en este ejemplo, texto simple)
    $content = "Este es un archivo de ejemplo creado desde Laravel OAuth Manager.\n";
    $content .= "Fecha de creación: " . date('Y-m-d H:i:s') . "\n";

    // Subir el archivo
    $result = $driveService->files->create($file, [
        'data' => $content,
        'mimeType' => 'text/plain',
        'uploadType' => 'multipart'
    ]);

    echo "Archivo subido exitosamente!\n";
    echo "ID del archivo: " . $result->getId() . "\n";
    echo "Nombre: " . $result->getName() . "\n";
    echo "Tamaño: " . $result->getSize() . " bytes\n";

    // === EJEMPLO 2: Listar archivos ===
    echo "\n=== LISTAR ARCHIVOS ===\n";
    
    $files = $driveService->files->listFiles([
        'pageSize' => 10,
        'fields' => 'nextPageToken, files(id, name, size, mimeType, createdTime)'
    ]);

    if (count($files->getFiles()) == 0) {
        echo "No se encontraron archivos.\n";
    } else {
        echo "Archivos encontrados:\n";
        foreach ($files->getFiles() as $file) {
            echo "- " . $file->getName() . " (ID: " . $file->getId() . ")\n";
            echo "  Tipo: " . $file->getMimeType() . "\n";
            echo "  Tamaño: " . ($file->getSize() ? formatBytes($file->getSize()) : 'N/A') . "\n";
            echo "  Creado: " . $file->getCreatedTime() . "\n\n";
        }
    }

    // === EJEMPLO 3: Buscar archivos ===
    echo "\n=== BUSCAR ARCHIVOS ===\n";
    
    $searchResults = $driveService->files->listFiles([
        'q' => "name contains 'documento'",
        'pageSize' => 5,
        'fields' => 'files(id, name, mimeType)'
    ]);

    if (count($searchResults->getFiles()) == 0) {
        echo "No se encontraron archivos que contengan 'documento' en el nombre.\n";
    } else {
        echo "Archivos encontrados con 'documento' en el nombre:\n";
        foreach ($searchResults->getFiles() as $file) {
            echo "- " . $file->getName() . " (ID: " . $file->getId() . ")\n";
        }
    }

    // === EJEMPLO 4: Crear una carpeta ===
    echo "\n=== CREAR CARPETA ===\n";
    
    $folder = new Google_Service_Drive_DriveFile();
    $folder->setName('Mi Carpeta Laravel');
    $folder->setMimeType('application/vnd.google-apps.folder');
    $folder->setDescription('Carpeta creada desde Laravel OAuth Manager');

    $createdFolder = $driveService->files->create($folder);
    
    echo "Carpeta creada exitosamente!\n";
    echo "ID de la carpeta: " . $createdFolder->getId() . "\n";
    echo "Nombre: " . $createdFolder->getName() . "\n";

    // === EJEMPLO 5: Obtener información de un archivo específico ===
    echo "\n=== INFORMACIÓN DE ARCHIVO ===\n";
    
    if (isset($result)) {
        $fileInfo = $driveService->files->get($result->getId(), [
            'fields' => 'id, name, size, mimeType, createdTime, modifiedTime, owners'
        ]);

        echo "Información detallada del archivo subido:\n";
        echo "ID: " . $fileInfo->getId() . "\n";
        echo "Nombre: " . $fileInfo->getName() . "\n";
        echo "Tipo MIME: " . $fileInfo->getMimeType() . "\n";
        echo "Tamaño: " . formatBytes($fileInfo->getSize()) . "\n";
        echo "Creado: " . $fileInfo->getCreatedTime() . "\n";
        echo "Modificado: " . $fileInfo->getModifiedTime() . "\n";
        
        $owners = $fileInfo->getOwners();
        if (!empty($owners)) {
            echo "Propietario: " . $owners[0]->getDisplayName() . " (" . $owners[0]->getEmailAddress() . ")\n";
        }
    }

    // === EJEMPLO 6: Descargar un archivo ===
    echo "\n=== DESCARGAR ARCHIVO ===\n";
    
    if (isset($result)) {
        $fileContent = $driveService->files->get($result->getId(), [
            'alt' => 'media'
        ]);

        echo "Contenido del archivo descargado:\n";
        echo "---\n";
        echo $fileContent->getBody()->getContents();
        echo "---\n";
    }

} catch (Exception $e) {
    echo "Error al usar la API de Google Drive: " . $e->getMessage() . "\n";
    echo "Detalles: " . $e->getTraceAsString() . "\n";
}

/**
 * Función auxiliar para formatear bytes
 */
function formatBytes($bytes, $precision = 2) {
    $units = array('B', 'KB', 'MB', 'GB', 'TB');
    
    for ($i = 0; $bytes > 1024 && $i < count($units) - 1; $i++) {
        $bytes /= 1024;
    }
    
    return round($bytes, $precision) . ' ' . $units[$i];
}

echo "\n=== EJEMPLO COMPLETADO ===\n";
echo "Para más información sobre la API de Google Drive, visita:\n";
echo "https://developers.google.com/drive/api/v3/reference\n";
