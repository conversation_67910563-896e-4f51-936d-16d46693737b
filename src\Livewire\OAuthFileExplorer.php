<?php

namespace LBCDev\OAuthFileExplorer\Livewire;

use Livewire\Component;
use LBCDev\OAuthFileExplorer\Services\FileExplorerFactory;
use LBCDev\OAuthFileExplorer\Contracts\FileExplorerInterface;
use LBCDev\OAuthManager\Models\OAuthService;

class OAuthFileExplorer extends Component
{
    public $oauthServiceId;
    public $currentFolderId = 'root';
    public $files = [];
    public $breadcrumb = [];
    public $selectedFile = null;
    public $selectedFileId = null;
    public $selectedFileUrl = '';
    public $searchQuery = '';
    public $isLoading = false;
    public $error = null;
    public $fieldName;
    public $serviceType = null;
    public $acceptedMimeTypes = [];

    protected $listeners = ['fileSelected'];
    protected ?FileExplorerInterface $explorer = null;

    public function mount($oauthServiceId = null, $fieldName = 'url', $acceptedMimeTypes = [])
    {
        $this->oauthServiceId = $oauthServiceId;
        $this->fieldName = $fieldName;
        $this->acceptedMimeTypes = $acceptedMimeTypes;

        if ($this->oauthServiceId) {
            $this->initializeExplorer();
            $this->loadFiles();
        }
    }

    protected function initializeExplorer(): void
    {
        try {
            $oauthService = OAuthService::find($this->oauthServiceId);

            if (!$oauthService) {
                throw new \Exception('OAuth service not found');
            }

            $this->serviceType = $oauthService->service_type;
            $this->explorer = FileExplorerFactory::create($oauthService);
        } catch (\Exception $e) {
            $this->error = 'Error initializing explorer: ' . $e->getMessage();
            $this->explorer = null;
        }
    }

    public function selectFile($fileId, $fileName, $fileUrl, $mimeType)
    {
        // Check if file type is accepted
        if (!empty($this->acceptedMimeTypes) && !in_array($mimeType, $this->acceptedMimeTypes)) {
            $this->error = "File type '{$mimeType}' is not accepted.";
            return;
        }

        $this->selectedFile = [
            'id' => $fileId,
            'name' => $fileName,
            'url' => $fileUrl,
            'mimeType' => $mimeType
        ];

        $this->selectedFileUrl = $fileUrl;
        $this->error = null;

        // Dispatch JavaScript event directly
        $eventName = 'fileSelected-' . str_replace('.', '-', $this->fieldName);
        $this->js("
        window.dispatchEvent(new CustomEvent('{$eventName}', {
            detail: { 
                fileUrl: '{$fileUrl}',
                fileId: '{$fileId}',
                fileName: '" . addslashes($fileName) . "',
                mimeType: '{$mimeType}'
            }
        }));
        console.log('Event dispatched:', '{$eventName}', 'with URL:', '{$fileUrl}');
    ");
    }

    public function openFolder($folderId)
    {
        $this->currentFolderId = $folderId;
        $this->searchQuery = '';
        $this->loadFiles();
    }

    public function loadFiles()
    {
        if (!$this->explorer) {
            $this->initializeExplorer();
        }

        if (!$this->explorer) {
            return;
        }

        $this->isLoading = true;
        $this->error = null;

        try {
            // Load files
            $result = $this->explorer->listFiles($this->currentFolderId);
            $this->files = $result['files'] ?? [];

            // Load breadcrumb
            $this->breadcrumb = $this->explorer->getBreadcrumb($this->currentFolderId);
        } catch (\Exception $e) {
            $this->error = 'Error loading files: ' . $e->getMessage();
            $this->files = [];
            $this->breadcrumb = [];
        }

        $this->isLoading = false;
    }

    public function search()
    {
        if (empty($this->searchQuery)) {
            $this->loadFiles();
            return;
        }

        if (!$this->explorer) {
            return;
        }

        $this->isLoading = true;
        $this->error = null;

        try {
            $this->files = $this->explorer->searchFiles($this->searchQuery, $this->currentFolderId);
        } catch (\Exception $e) {
            $this->error = 'Error searching files: ' . $e->getMessage();
            $this->files = [];
        }

        $this->isLoading = false;
    }

    public function clearSearch()
    {
        $this->searchQuery = '';
        $this->loadFiles();
    }

    public function testConnection()
    {
        if (!$this->explorer) {
            $this->initializeExplorer();
        }

        if (!$this->explorer) {
            return false;
        }

        try {
            return $this->explorer->testConnection();
        } catch (\Exception $e) {
            $this->error = 'Connection test failed: ' . $e->getMessage();
            return false;
        }
    }

    public function getServiceTypeDisplayName(): string
    {
        return match ($this->serviceType) {
            'google_drive' => 'Google Drive',
            'dropbox' => 'Dropbox',
            'onedrive' => 'OneDrive',
            'youtube' => 'YouTube',
            'mega' => 'Mega',
            default => ucfirst(str_replace('_', ' ', $this->serviceType ?? 'Unknown'))
        };
    }

    public function render()
    {
        return view('oauth-file-explorer::livewire.oauth-file-explorer');
    }
}
