## Configuración de Google OAuth

### 1. Crear proyecto en Google Cloud Console

1. Ve a [Google Cloud Console](https://console.cloud.google.com/)
2. Crear nuevo proyecto o seleccionar uno existente
3. Habilitar las APIs necesarias:
   -  Google Drive API
   -  Google OAuth 2.0 API

### 2. Configurar OAuth

1. **Pantalla de consentimiento OAuth**:

   -  Tipo: Externo
   -  Nombre de la aplicación
   -  Email de soporte
   -  Dominios autorizados

2. **Crear credenciales OAuth**:

   -  Tipo: Aplicación web
   -  URIs de redirección autorizados:
      -  **Desarrollo (con fwd.host):** `https://fwd.host/https://tu-dominio.test/oauth-manager/callback/servicio-slug`
      -  **Producción:** `https://tu-dominio.com/oauth-manager/callback/servicio-slug`

   > **Nota:** Reemplaza `servicio-slug` con el slug único de cada servicio OAuth que crees. Puedes registrar múltiples URIs para diferentes servicios usando el mismo cliente OAuth.

### 3. Configurar scopes

Los scopes más comunes para Google Drive:

```php
'scopes' => [
    'https://www.googleapis.com/auth/drive.file',           // Acceso a archivos creados por la app
    'https://www.googleapis.com/auth/drive.metadata.readonly', // Metadatos de solo lectura
    'https://www.googleapis.com/auth/drive',                   // Acceso completo a Drive
],
```

## Configuración de YouTube OAuth

### 1. Crear proyecto en Google Cloud Console

YouTube utiliza la misma infraestructura OAuth de Google, por lo que necesitas configurar un proyecto en Google Cloud Console:

1. Ve a [Google Cloud Console](https://console.cloud.google.com/)
2. Crear nuevo proyecto o seleccionar uno existente
3. Habilitar las APIs necesarias:
   -  YouTube Data API v3
   -  Google OAuth 2.0 API

### 2. Configurar OAuth

1. **Pantalla de consentimiento OAuth**:

   -  Tipo: Externo
   -  Nombre de la aplicación
   -  Email de soporte
   -  Dominios autorizados
   -  Scopes: Añadir los scopes de YouTube que necesites

2. **Crear credenciales OAuth**:

   -  Tipo: Aplicación web
   -  URIs de redirección autorizados:
      -  **Desarrollo (con fwd.host):** `https://fwd.host/https://tu-dominio.test/oauth-manager/callback/youtube-slug`
      -  **Producción:** `https://tu-dominio.com/oauth-manager/callback/youtube-slug`

   > **Nota:** Reemplaza `youtube-slug` con el slug único de cada servicio YouTube que crees. Puedes usar el mismo cliente OAuth para múltiples servicios YouTube.

### 3. Configurar scopes

Los scopes más comunes para YouTube:

```php
'scopes' => [
    'https://www.googleapis.com/auth/youtube.readonly',        // Leer datos del canal y videos
    'https://www.googleapis.com/auth/youtube.upload',          // Subir videos
    'https://www.googleapis.com/auth/youtube.force-ssl',       // Acceso completo con SSL
    'https://www.googleapis.com/auth/youtube',                 // Acceso completo a YouTube
    'https://www.googleapis.com/auth/youtubepartner',          // Para partners de YouTube
],
```

### 4. Configurar variables de entorno

Añade a tu archivo `.env`:

```env
# YouTube
YOUTUBE_CLIENT_ID=tu-client-id-de-google
YOUTUBE_CLIENT_SECRET=tu-client-secret-de-google
YOUTUBE_REDIRECT_URI=https://tu-dominio.com/oauth-manager/callback/youtube
YOUTUBE_USE_FWD_SERVICE_FOR_LOCAL_OAUTH=false
```

### 5. Consideraciones especiales para YouTube

**Cuotas y límites:**

1. **Cuota diaria**: YouTube API tiene límites de cuota diarios (10,000 unidades por defecto)
2. **Rate limiting**: Máximo 100 requests por 100 segundos por usuario
3. **Verificación de aplicación**: Para ciertos scopes, Google requiere verificación de la aplicación

**Scopes sensibles que requieren verificación:**

-  `youtube.upload` - Requiere verificación para aplicaciones en producción
-  `youtube.force-ssl` - Requiere verificación para acceso completo
-  `youtubepartner` - Solo para partners oficiales de YouTube

**Desarrollo vs Producción:**

-  **Desarrollo**: Puedes usar hasta 100 usuarios de prueba sin verificación
-  **Producción**: Necesitas verificación de Google para scopes sensibles

### 6. Ejemplo de configuración completa

```php
// En config/oauth-manager.php
'youtube' => [
    'name' => 'YouTube',
    'slug' => 'youtube',
    'icon' => 'heroicon-o-play',
    'provider' => \LBCDev\OAuthManager\Providers\YouTubeProvider::class,
    'scopes' => [
        'https://www.googleapis.com/auth/youtube.readonly',
        'https://www.googleapis.com/auth/youtube.upload',
        'https://www.googleapis.com/auth/youtube.force-ssl'
    ],
    'fields' => [
        'client_id' => env('YOUTUBE_CLIENT_ID'),
        'client_secret' => env('YOUTUBE_CLIENT_SECRET'),
        'redirect_uri' => env('YOUTUBE_REDIRECT_URI'),
        'use_fwd_service_for_local_oauth' => env('YOUTUBE_USE_FWD_SERVICE_FOR_LOCAL_OAUTH', false),
    ]
],
```

## Configuración de OneDrive OAuth

### 1. Crear aplicación en Microsoft Azure

1. Ve a [Azure Portal](https://portal.azure.com/)
2. Navega a **Azure Active Directory** > **App registrations**
3. Haz clic en **New registration**
4. Configura:
   -  **Name**: Nombre de tu aplicación
   -  **Supported account types**: Accounts in any organizational directory and personal Microsoft accounts
   -  **Redirect URI**: Web - `https://tu-dominio.com/oauth-manager/callback/onedrive`

### 2. Configurar credenciales

1. **Obtener Application (client) ID**:

   -  En la página **Overview** de tu aplicación
   -  Copia el **Application (client) ID**

2. **Crear Client Secret**:
   -  Ve a **Certificates & secrets**
   -  Haz clic en **New client secret**
   -  Añade una descripción y selecciona expiración
   -  Copia el **Value** (no el Secret ID)

### 3. Configurar permisos API

1. Ve a **API permissions**
2. Haz clic en **Add a permission**
3. Selecciona **Microsoft Graph**
4. Selecciona **Delegated permissions**
5. Añade los siguientes permisos:

   -  `Files.Read` - Leer archivos del usuario
   -  `Files.ReadWrite` - Leer y escribir archivos del usuario
   -  `User.Read` - Leer perfil básico del usuario

6. Haz clic en **Grant admin consent** (si tienes permisos de administrador)

### 4. Configurar redirect URIs

En **Authentication** > **Redirect URIs**, añade:

-  **Desarrollo (con fwd.host):** `https://fwd.host/https://tu-dominio.test/oauth-manager/callback/onedrive`
-  **Producción:** `https://tu-dominio.com/oauth-manager/callback/onedrive`

### 5. Configurar scopes

Los scopes más comunes para OneDrive:

```php
'scopes' => [
    'https://graph.microsoft.com/Files.Read',      // Leer archivos
    'https://graph.microsoft.com/Files.ReadWrite', // Leer y escribir archivos
    'https://graph.microsoft.com/User.Read',       // Información básica del usuario
],
```

## Configuración de Dropbox OAuth

### 1. Crear aplicación en Dropbox App Console

1. Ve a [Dropbox App Console](https://www.dropbox.com/developers/apps)
2. Haz clic en **Create app**
3. Configura:
   -  **Choose an API**: Scoped access
   -  **Choose the type of access you need**:
      -  **App folder**: Solo acceso a una carpeta específica de tu app
      -  **Full Dropbox**: Acceso completo a la cuenta de Dropbox del usuario
   -  **Name your app**: Nombre único para tu aplicación

### 2. Configurar credenciales

1. **Obtener App key y App secret**:

   -  En la página de configuración de tu app, encontrarás:
      -  **App key** (equivalente a client_id)
      -  **App secret** (equivalente a client_secret)

2. **Configurar Redirect URIs**:
   -  En la sección **OAuth 2**, añade las URIs de redirección:
   -  **Desarrollo (con fwd.host):** `https://fwd.host/https://tu-dominio.test/oauth-manager/callback/dropbox`
   -  **Producción:** `https://tu-dominio.com/oauth-manager/callback/dropbox`

### 3. Configurar permisos

En la sección **Permissions**, habilita los scopes necesarios:

-  **files.content.write**: Escribir contenido de archivos
-  **files.content.read**: Leer contenido de archivos
-  **files.metadata.read**: Leer metadatos de archivos
-  **files.metadata.write**: Escribir metadatos de archivos

### 4. Configurar scopes

Los scopes más comunes para Dropbox:

```php
'scopes' => [
    'files.content.write',    // Escribir archivos
    'files.content.read',     // Leer archivos
    'files.metadata.read',    // Leer metadatos de archivos
],
```

> **Nota importante**: Dropbox requiere que solicites explícitamente el `token_access_type=offline` para obtener refresh tokens. Esto se maneja automáticamente en el DropboxProvider.

## Configuración de Mega

### 1. Crear cuenta en Mega

1. Ve a [Mega.nz](https://mega.nz/) y crea una cuenta
2. Verifica tu email si es necesario
3. Asegúrate de tener acceso a tu email y password

### 2. Configurar credenciales

A diferencia de otros servicios OAuth, Mega no utiliza un sistema OAuth tradicional. En su lugar, utiliza autenticación directa con email y password:

1. **Email**: Tu dirección de email de Mega
2. **Password**: Tu contraseña de Mega

> **Importante**: Las credenciales se almacenan de forma segura y encriptada en la base de datos. Nunca se transmiten en texto plano.

### 3. Configurar variables de entorno

Añade a tu archivo `.env`:

```env
# Mega
MEGA_EMAIL=<EMAIL>
MEGA_PASSWORD=tu-password-mega
MEGA_REDIRECT_URI=https://tu-dominio.com/oauth-manager/callback/mega
MEGA_USE_FWD_SERVICE_FOR_LOCAL_OAUTH=false
```

### 4. Configurar redirect URIs

Para desarrollo local con fwd.host:

```
https://fwd.host/https://tu-dominio.test/oauth-manager/callback/mega
```

Para producción:

```
https://tu-dominio.com/oauth-manager/callback/mega
```

### 5. Consideraciones de seguridad

**Mega requiere consideraciones especiales de seguridad:**

1. **Autenticación de dos factores**: Si tienes 2FA habilitado en Mega, necesitarás deshabilitarlo temporalmente para la integración automática, o manejar el 2FA programáticamente.

2. **Contraseñas de aplicación**: Mega no ofrece contraseñas de aplicación específicas, por lo que debes usar tu contraseña principal.

3. **Almacenamiento seguro**: Las credenciales se almacenan encriptadas en la base de datos usando el sistema de encriptación de Laravel.

4. **Limitaciones de API**: La API de Mega tiene limitaciones de rate limiting. Implementa retry logic en tu aplicación.

### 6. Configurar scopes

Aunque Mega no usa scopes tradicionales de OAuth, el paquete mantiene la estructura para consistencia:

```php
'scopes' => [
    'files.read',     // Leer archivos y carpetas
    'files.write',    // Escribir y modificar archivos
],
```

### 7. Diferencias con OAuth tradicional

**Mega funciona diferente a otros servicios:**

-  **No hay registro de aplicación**: No necesitas registrar tu aplicación en un portal de desarrolladores
-  **Autenticación directa**: Usa email/password directamente
-  **Tokens de sesión**: Mega genera tokens de sesión que pueden durar indefinidamente
-  **Sin refresh tokens**: No hay refresh tokens tradicionales, pero puedes recrear la sesión cuando sea necesario

### 8. Ejemplo de configuración completa

```php
// En config/oauth-manager.php
'mega' => [
    'name' => 'Mega',
    'slug' => 'mega',
    'icon' => 'heroicon-o-cloud-arrow-up',
    'provider' => \LBCDev\OAuthManager\Providers\MegaProvider::class,
    'scopes' => [
        'files.read',
        'files.write'
    ],
    'fields' => [
        'email' => env('MEGA_EMAIL'),
        'password' => env('MEGA_PASSWORD'),
        'redirect_uri' => env('MEGA_REDIRECT_URI'),
        'use_fwd_service_for_local_oauth' => env('MEGA_USE_FWD_SERVICE_FOR_LOCAL_OAUTH', false),
    ]
],
```
