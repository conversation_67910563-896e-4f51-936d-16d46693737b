<?php

namespace LBCDev\OAuthManager\Console\Commands;

use Illuminate\Console\Command;
use LBCDev\OAuthManager\Models\OAuthService;

class ShowOAuthServicesCommand extends Command
{
    protected $signature = 'oauth:show {slug?} {--full-tokens : Show complete tokens without truncation}';
    protected $description = 'Show OAuth services information';

    public function handle(): void
    {
        $slug = $this->argument('slug');

        if ($slug) {
            $this->showSingleService($slug);
        } else {
            $this->showAllServices();
        }
    }

    private function showSingleService(string $slug): void
    {
        $service = OAuthService::where('slug', $slug)->first();

        if (!$service) {
            $this->error("❌ Servicio con slug '{$slug}' no encontrado");
            return;
        }

        $this->info("📋 Información del servicio OAuth");
        $this->line('');

        $this->displayServiceInfo($service);
    }

    private function showAllServices(): void
    {
        $services = OAuthService::orderBy('name')->get();

        if ($services->isEmpty()) {
            $this->warn('⚠️  No hay servicios OAuth configurados');
            return;
        }

        $this->info("📋 Lista de servicios OAuth ({$services->count()} servicios)");
        $this->line('');

        foreach ($services as $service) {
            $this->displayServiceInfo($service);
            $this->line('');
        }
    }

    private function displayServiceInfo(OAuthService $service): void
    {
        // Información básica
        $this->line("🔗 <info>{$service->name}</info>");
        $this->line("   Slug: <comment>{$service->slug}</comment>");
        $this->line("   Tipo: <comment>{$service->service_type}</comment>");

        // Estado del servicio
        $statusIcon = $service->is_active ? '✅' : '❌';
        $statusText = $service->is_active ? 'Activo' : 'Inactivo';
        $this->line("   Estado: {$statusIcon} <comment>{$statusText}</comment>");

        // Estado del token
        $this->displayTokenStatus($service);

        // Tokens (truncados por seguridad)
        $this->displayTokens($service);

        // Fechas importantes
        $this->displayDates($service);
    }

    private function displayTokenStatus(OAuthService $service): void
    {
        if (!$service->access_token) {
            $this->line("   Token: ❌ <fg=red>No autorizado</fg=red>");
            return;
        }

        if ($service->isTokenExpired()) {
            if ($service->refresh_token) {
                $this->line("   Token: ⏰ <fg=yellow>Expirado (puede refrescarse)</fg=yellow>");
            } else {
                $this->line("   Token: ⏰ <fg=red>Expirado (sin refresh token)</fg=red>");
            }
        } else {
            $this->line("   Token: ✅ <fg=green>Válido</fg=green>");
        }
    }

    private function displayTokens(OAuthService $service): void
    {
        $showFullTokens = $this->option('full-tokens');

        // Access Token
        if ($service->access_token) {
            $displayToken = $showFullTokens ? $service->access_token : $this->truncateToken($service->access_token);
            $this->line("   Access Token: <comment>{$displayToken}</comment>");
        } else {
            $this->line("   Access Token: <fg=red>No disponible</fg=red>");
        }

        // Refresh Token
        if ($service->refresh_token) {
            $displayRefreshToken = $showFullTokens ? $service->refresh_token : $this->truncateToken($service->refresh_token);
            $this->line("   Refresh Token: <comment>{$displayRefreshToken}</comment>");
        } else {
            $this->line("   Refresh Token: <fg=red>No disponible</fg=red>");
        }
    }

    private function displayDates(OAuthService $service): void
    {
        // Fecha de expiración
        if ($service->expires_at) {
            $expiresAt = $service->expires_at->format('Y-m-d H:i:s');
            $this->line("   Expira: <comment>{$expiresAt}</comment>");
        } else {
            $this->line("   Expira: <comment>Sin expiración</comment>");
        }

        // Último uso
        if ($service->last_used_at) {
            $lastUsed = $service->last_used_at->format('Y-m-d H:i:s');
            $this->line("   Último uso: <comment>{$lastUsed}</comment>");
        } else {
            $this->line("   Último uso: <comment>Nunca</comment>");
        }

        // Fechas de creación y actualización
        $createdAt = $service->created_at->format('Y-m-d H:i:s');
        $updatedAt = $service->updated_at->format('Y-m-d H:i:s');
        $this->line("   Creado: <comment>{$createdAt}</comment>");
        $this->line("   Actualizado: <comment>{$updatedAt}</comment>");
    }

    private function truncateToken(string $token): string
    {
        if (strlen($token) <= 20) {
            return $token;
        }

        return substr($token, 0, 10) . '...' . substr($token, -10);
    }
}
