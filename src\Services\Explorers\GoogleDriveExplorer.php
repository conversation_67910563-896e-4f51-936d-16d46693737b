<?php

namespace LBCDev\OAuthFileExplorer\Services\Explorers;

use LBCDev\OAuthFileExplorer\Services\AbstractFileExplorer;
use Google\Service\Drive;
use Google\Service\Drive\DriveFile;

class GoogleDriveExplorer extends AbstractFileExplorer
{
    protected $driveService;

    /**
     * Initialize Google Drive service with OAuth token
     */
    protected function initializeService(): void
    {
        $this->ensureValidToken();

        // $provider = $this->oauthService->getProviderInstance();
        $client = new \Google_Client();
        $client->setAccessToken($this->oauthService->access_token);
        $this->driveService = new Drive($client);
    }

    public function getServiceType(): string
    {
        return 'google_drive';
    }

    protected function getFolderMimeType(): string
    {
        return 'application/vnd.google-apps.folder';
    }

    /**
     * List files and folders in a specific folder
     */
    public function listFiles(string $folderId = 'root', int $pageSize = 50): array
    {
        $this->ensureValidToken();

        if (!$this->driveService) {
            $this->initializeService();
        }

        try {
            $optParams = [
                'pageSize' => $pageSize,
                'fields' => 'nextPageToken, files(id, name, mimeType, size, modifiedTime, webViewLink, webContentLink, parents, iconLink)',
                'q' => "'{$folderId}' in parents and trashed = false",
                'orderBy' => 'folder,name'
            ];

            $results = $this->driveService->files->listFiles($optParams);
            $files = $results->getFiles();

            $formattedFiles = [];
            foreach ($files as $file) {
                $formattedFiles[] = $this->formatFileData($file);
            }

            return [
                'files' => $formattedFiles,
                'nextPageToken' => $results->getNextPageToken()
            ];
        } catch (\Exception $e) {
            throw new \Exception('Error listing files: ' . $e->getMessage());
        }
    }

    /**
     * Get file information by ID
     */
    public function getFile(string $fileId): array
    {
        $this->ensureValidToken();

        if (!$this->driveService) {
            $this->initializeService();
        }

        try {
            $file = $this->driveService->files->get($fileId, [
                'fields' => 'id, name, mimeType, size, modifiedTime, webViewLink, webContentLink, parents, iconLink'
            ]);

            return $this->formatFileData($file);
        } catch (\Exception $e) {
            throw new \Exception('Error getting file: ' . $e->getMessage());
        }
    }

    /**
     * Get folder breadcrumb path
     */
    public function getBreadcrumb(string $folderId): array
    {
        if (!$this->driveService || $folderId === 'root') {
            return [['id' => 'root', 'name' => 'My Drive']];
        }

        try {
            $breadcrumb = [];
            $currentId = $folderId;

            while ($currentId !== 'root') {
                $file = $this->driveService->files->get($currentId, ['fields' => 'id, name, parents']);

                array_unshift($breadcrumb, [
                    'id' => $file->getId(),
                    'name' => $file->getName()
                ]);

                $parents = $file->getParents();
                $currentId = $parents ? $parents[0] : 'root';
            }

            array_unshift($breadcrumb, ['id' => 'root', 'name' => 'My Drive']);

            return $breadcrumb;
        } catch (\Exception) {
            return [['id' => 'root', 'name' => 'My Drive']];
        }
    }

    /**
     * Search files by name
     */
    public function searchFiles(string $query, string $folderId = 'root'): array
    {
        $this->ensureValidToken();

        if (!$this->driveService) {
            $this->initializeService();
        }

        try {
            $searchQuery = "name contains '{$query}' and trashed = false";
            if ($folderId !== 'root') {
                $searchQuery .= " and '{$folderId}' in parents";
            }

            $optParams = [
                'pageSize' => 50,
                'fields' => 'files(id, name, mimeType, size, modifiedTime, webViewLink, webContentLink, parents, iconLink)',
                'q' => $searchQuery,
                'orderBy' => 'folder,name'
            ];

            $results = $this->driveService->files->listFiles($optParams);
            $files = $results->getFiles();

            $formattedFiles = [];
            foreach ($files as $file) {
                $formattedFiles[] = $this->formatFileData($file);
            }

            return $formattedFiles;
        } catch (\Exception $e) {
            throw new \Exception('Error searching files: ' . $e->getMessage());
        }
    }

    /**
     * Format file data for consistent output
     */
    protected function formatFileData($file): array
    {
        if (!$file instanceof DriveFile) {
            throw new \InvalidArgumentException('Expected DriveFile instance');
        }

        $isFolder = $this->isFolder($file->getMimeType());

        return $this->getStandardFileStructure(
            $file->getId(),
            $file->getName(),
            $file->getMimeType(),
            $isFolder,
            [
                'size' => $file->getSize() ? (int) $file->getSize() : 0,
                'sizeFormatted' => $file->getSize() ? $this->formatFileSize((int) $file->getSize()) : '-',
                'modifiedTime' => $file->getModifiedTime(),
                'webViewLink' => $file->getWebViewLink(),
                'webContentLink' => $file->getWebContentLink(),
                'iconLink' => $file->getIconLink(),
                'parents' => $file->getParents() ?: [],
                'downloadUrl' => $isFolder ? null : $file->getWebContentLink()
            ]
        );
    }

    /**
     * Perform service-specific connection test
     */
    protected function performConnectionTest(): bool
    {
        try {
            if (!$this->driveService) {
                $this->initializeService();
            }

            $this->driveService->about->get(['fields' => 'user']);
            return true;
        } catch (\Exception) {
            return false;
        }
    }

    /**
     * Format file size in human readable format
     */
    protected function formatFileSize(int $bytes): string
    {
        if ($bytes === 0) return '0 B';

        $units = ['B', 'KB', 'MB', 'GB', 'TB'];
        $power = floor(log($bytes, 1024));
        $power = min($power, count($units) - 1);

        $size = $bytes / pow(1024, $power);
        $formattedSize = round($size, 2);

        return $formattedSize . ' ' . $units[$power];
    }

    /**
     * Get supported file types for Google Drive
     */
    public function getSupportedMimeTypes(): array
    {
        return [
            // Documents
            'application/pdf',
            'application/msword',
            'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
            'application/vnd.ms-excel',
            'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
            'application/vnd.ms-powerpoint',
            'application/vnd.openxmlformats-officedocument.presentationml.presentation',
            'text/plain',
            'text/csv',

            // Google Workspace files
            'application/vnd.google-apps.document',
            'application/vnd.google-apps.spreadsheet',
            'application/vnd.google-apps.presentation',
            'application/vnd.google-apps.form',
            'application/vnd.google-apps.drawing',

            // Images
            'image/jpeg',
            'image/png',
            'image/gif',
            'image/bmp',
            'image/svg+xml',

            // Videos
            'video/mp4',
            'video/avi',
            'video/mov',
            'video/wmv',
            'video/flv',
            'video/webm',
            'video/mkv',

            // Audio
            'audio/mp3',
            'audio/wav',
            'audio/ogg',
            'audio/aac',
            'audio/flac',
            'audio/wma',

            // Archives
            'application/zip',
            'application/x-rar-compressed',
            'application/x-7z-compressed',

            // Folders
            'application/vnd.google-apps.folder'
        ];
    }

    public function downloadFile(string $fileId)
    {
        $this->ensureValidToken();

        if (!$this->driveService) {
            $this->initializeService();
        }

        try {
            $file = $this->driveService->files->get($fileId, ['alt' => 'media']);
            return $file->getBody()->getContents();
        } catch (\Exception $e) {
            throw new \Exception('Error downloading file: ' . $e->getMessage());
        }
    }
}
