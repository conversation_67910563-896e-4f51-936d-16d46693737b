<?php

namespace LBCDev\OAuthManager\Console\Commands;

use Illuminate\Console\Command;
use LBCDev\OAuthManager\Models\OAuthService;


class TestOAuthConnectionCommand extends Command
{
    protected $signature = 'oauth:test {service_id} {--refresh}';
    protected $description = 'Test OAuth connection for a service';

    public function handle(): void
    {
        $this->info('🔍 Probando conexiones...');

        $serviceId = $this->argument('service_id');
        $service = OAuthService::find($serviceId);

        if (!$service) {
            $this->error("❌ Servicio con ID {$serviceId} no encontrado");
            return;
        }

        $this->info("🔗 Probando conexión para: {$service->name} ({$service->service_type})");

        if (!$service->access_token) {
            $this->error('🚫 Servicio no autorizado. Por favor autorízalo primero.');
            return;
        }

        if ($service->isTokenExpired()) {
            $this->warn('⏰ Token expirado.');

            if ($this->option('refresh') && $service->refresh_token) {
                $this->info('♻️ Intentando refrescar token...');

                if ($service->ensureValidToken()) {
                    $this->info('✅ Token refrescado con éxito!');
                } else {
                    $this->error('❌ No se pudo refrescar el token. Por favor reautoriza.');
                    return;
                }
            } else {
                $this->error('❌ Token expirado. Usa --refresh o reautoriza.');
                return;
            }
        }

        try {
            $provider = $service->getProviderInstance();

            // Solo MegaProvider tiene el método testConnection disponible
            if ($service->service_type === 'mega' && method_exists($provider, 'testConnection')) {
                if ($provider->testConnection()) {
                    $this->info('✅ Conexión exitosa!');
                } else {
                    $this->error('❌ La prueba de conexión falló');
                }
            } else {
                // Para otros providers, verificar que el token sea válido
                if ($service->ensureValidToken()) {
                    $this->info('✅ Token válido - Servicio configurado correctamente!');
                    $this->info('ℹ️  Para pruebas específicas de API, usa los ejemplos en docs/examples/');
                } else {
                    $this->error('❌ Token inválido o expirado');
                }
            }
        } catch (\Exception $e) {
            $this->error('❌ Error en la prueba de conexión: ' . $e->getMessage());
        }
    }
}
