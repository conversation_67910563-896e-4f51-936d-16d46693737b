<?php

/**
 * Ejemplos de uso del sistema de redirección OAuth Manager
 * 
 * Este archivo muestra diferentes formas de controlar la redirección
 * después de completar la autorización OAuth.
 */

namespace Examples;

use Illuminate\Http\Request;
use Illuminate\Http\RedirectResponse;
use LBCDev\OAuthManager\Models\OAuthService;

class RedirectionExamples
{
    /**
     * Ejemplo 1: Redirección básica con URL por defecto en configuración
     * 
     * En tu .env:
     * OAUTH_MANAGER_DEFAULT_REDIRECT_URL=/admin
     */
    public function basicRedirection()
    {
        $service = OAuthService::find(1);
        
        // Simplemente redirige a la autorización
        // Después del callback, redirigirá a la URL configurada en .env
        return redirect()->route('oauth-manager.authorize', ['service' => $service]);
    }

    /**
     * Ejemplo 2: Redirección específica por parámetro
     * 
     * Útil cuando quieres redirigir a una página específica después de la autorización
     */
    public function specificRedirection()
    {
        $service = OAuthService::find(1);
        
        // Redirige a una página específica después de la autorización
        return redirect()->route('oauth-manager.authorize', [
            'service' => $service,
            'redirect_url' => '/admin/oauth-services'
        ]);
    }

    /**
     * Ejemplo 3: Redirección dinámica basada en el contexto
     * 
     * Para Filament Admin Panel
     */
    public function filamentRedirection(Request $request)
    {
        $service = OAuthService::find($request->service_id);
        
        // Determinar la URL de redirección basada en el contexto
        $redirectUrl = match($request->get('context')) {
            'settings' => '/admin/settings',
            'services' => '/admin/oauth-services',
            'dashboard' => '/admin',
            default => '/admin/oauth-services'
        };
        
        return redirect()->route('oauth-manager.authorize', [
            'service' => $service,
            'redirect_url' => $redirectUrl
        ]);
    }

    /**
     * Ejemplo 4: Redirección con parámetros adicionales
     * 
     * Útil cuando necesitas pasar información adicional después de la autorización
     */
    public function redirectionWithParameters(Request $request)
    {
        $service = OAuthService::find($request->service_id);
        $returnUrl = $request->get('return_url', '/dashboard');
        
        // Agregar parámetros a la URL de redirección
        $redirectUrl = $returnUrl . '?oauth_success=1&service_id=' . $service->id;
        
        return redirect()->route('oauth-manager.authorize', [
            'service' => $service,
            'redirect_url' => $redirectUrl
        ]);
    }

    /**
     * Ejemplo 5: Redirección condicional basada en el usuario
     * 
     * Diferentes redirecciones según el rol del usuario
     */
    public function conditionalRedirection(Request $request)
    {
        $service = OAuthService::find($request->service_id);
        $user = $request->user();
        
        // Determinar redirección basada en el rol del usuario
        $redirectUrl = match(true) {
            $user->hasRole('admin') => '/admin/oauth-services',
            $user->hasRole('manager') => '/manager/services',
            $user->hasRole('user') => '/dashboard/services',
            default => '/dashboard'
        };
        
        return redirect()->route('oauth-manager.authorize', [
            'service' => $service,
            'redirect_url' => $redirectUrl
        ]);
    }

    /**
     * Ejemplo 6: Manejo de errores con redirección personalizada
     * 
     * Cómo manejar la página después de la autorización para mostrar mensajes
     */
    public function handleOAuthCallback(Request $request): RedirectResponse
    {
        // Esta sería la página a la que redirige después del callback
        
        if ($request->session()->has('success')) {
            $message = $request->session()->get('success');
            // Mostrar mensaje de éxito
            return redirect('/dashboard')->with('notification', [
                'type' => 'success',
                'message' => $message
            ]);
        }
        
        if ($request->session()->has('error')) {
            $message = $request->session()->get('error');
            // Mostrar mensaje de error
            return redirect('/dashboard')->with('notification', [
                'type' => 'error',
                'message' => $message
            ]);
        }
        
        return redirect('/dashboard');
    }

    /**
     * Ejemplo 7: Integración con Livewire
     * 
     * Cómo usar la redirección en componentes Livewire
     */
    public function livewireIntegration()
    {
        // En tu componente Livewire
        /*
        public function authorizeService($serviceId)
        {
            $service = OAuthService::find($serviceId);
            
            // Redirigir de vuelta al componente actual
            $currentUrl = request()->url();
            
            return redirect()->route('oauth-manager.authorize', [
                'service' => $service,
                'redirect_url' => $currentUrl
            ]);
        }
        */
    }

    /**
     * Ejemplo 8: Redirección con estado personalizado
     * 
     * Guardar estado adicional para usar después de la autorización
     */
    public function redirectionWithState(Request $request)
    {
        $service = OAuthService::find($request->service_id);
        
        // Guardar estado adicional en la sesión
        session([
            'oauth_context' => [
                'action' => $request->get('action', 'connect'),
                'source_page' => $request->get('source_page'),
                'user_preferences' => $request->get('preferences', [])
            ]
        ]);
        
        return redirect()->route('oauth-manager.authorize', [
            'service' => $service,
            'redirect_url' => '/admin/oauth-services/callback-handler'
        ]);
    }

    /**
     * Ejemplo 9: Redirección para APIs
     * 
     * Cómo manejar redirecciones cuando se usa desde una API
     */
    public function apiRedirection(Request $request)
    {
        $service = OAuthService::find($request->service_id);
        
        // Para APIs, podrías querer redirigir a una página específica
        // que maneje la respuesta y la envíe de vuelta a la aplicación frontend
        $redirectUrl = '/api/oauth/callback?client_id=' . $request->get('client_id');
        
        return redirect()->route('oauth-manager.authorize', [
            'service' => $service,
            'redirect_url' => $redirectUrl
        ]);
    }

    /**
     * Ejemplo 10: Redirección con validación
     * 
     * Útil para validar la URL de redirección por seguridad
     */
    public function secureRedirection(Request $request)
    {
        $service = OAuthService::find($request->service_id);
        $redirectUrl = $request->get('redirect_url');
        
        // Lista de URLs permitidas para redirección
        $allowedUrls = [
            '/admin',
            '/admin/oauth-services',
            '/admin/settings',
            '/dashboard',
            '/dashboard/services'
        ];
        
        // Validar que la URL esté en la lista permitida
        if (!in_array($redirectUrl, $allowedUrls)) {
            $redirectUrl = '/admin'; // URL por defecto segura
        }
        
        return redirect()->route('oauth-manager.authorize', [
            'service' => $service,
            'redirect_url' => $redirectUrl
        ]);
    }
}
