# Changelog

All notable changes to this project will be documented in this file.

The format is based on [Keep a Changelog](https://keepachangelog.com/en/1.0.0/),
and this project adheres to [Semantic Versioning](https://semver.org/spec/v2.0.0.html).

## [Unreleased]

### Added
- **OneDrive Explorer**: Implementación base del explorer para OneDrive
  - Estructura completa siguiendo el patrón de GoogleDriveExplorer
  - Integración con Microsoft Graph SDK
  - Soporte para tipos MIME específicos de OneDrive
  - Tests unitarios completos
- **Configuración OneDrive**: Soporte en configuración para OneDrive
  - Actualizado `config/oauth-file-explorer.php`
  - Agregado OneDrive al FileExplorerFactory
  - Soporte en componente Livewire para OneDrive
- **Tests Automatizados**: Suite completa de tests para OneDrive
  - Tests unitarios para OneDriveExplorer
  - Tests de integración con FileExplorerFactory
  - Tests de componente Livewire actualizados
- **Documentación Completa**: README.md comprehensivo
  - Guía de instalación y configuración
  - Ejemplos de uso para todos los explorers
  - Documentación de API
  - Guías de contribución

### Changed
- **FileExplorerFactory**: Actualizado para incluir soporte de OneDrive
- **Componente Livewire**: Agregado soporte de visualización para OneDrive
- **Tests**: Actualizados tests existentes para incluir OneDrive

### Technical Details
- Agregada dependencia `microsoft/microsoft-graph` para OneDrive
- Implementación base con métodos placeholder para desarrollo futuro
- Estructura preparada para autenticación OAuth completa
- Tests diseñados para evitar dependencias complejas de Microsoft Graph SDK

## [1.0.0] - 2024-01-15

### Added
- **GoogleDriveExplorer**: Explorer completo para Google Drive
  - Listado de archivos y carpetas
  - Búsqueda de archivos
  - Navegación de breadcrumb
  - Descarga de archivos
- **DropboxExplorer**: Explorer completo para Dropbox
  - Integración con Spatie Dropbox Client
  - Soporte completo de funcionalidades
- **Componente Livewire**: Interfaz de usuario reactiva
  - Navegación de carpetas
  - Búsqueda en tiempo real
  - Selección de archivos
  - Filtros por tipo MIME
- **AbstractFileExplorer**: Clase base para todos los explorers
  - Interfaz unificada
  - Métodos comunes
  - Manejo de errores estándar
- **FileExplorerFactory**: Factory pattern para crear explorers
  - Creación automática basada en tipo de servicio
  - Soporte para múltiples proveedores
- **Tests Completos**: Suite de tests unitarios y de integración
  - Cobertura completa de funcionalidades
  - Tests de componente Livewire
  - Mocking apropiado para servicios externos

### Dependencies
- PHP 8.2+
- Laravel 10.0+
- Livewire 3.0+
- lbcdev/oauth-manager

## [0.1.0] - 2024-01-01

### Added
- Estructura inicial del proyecto
- Configuración básica de Laravel package
- Configuración de tests con Orchestra Testbench
