<?php

namespace LBCDev\OAuthFileExplorer\Contracts;

use LBCDev\OAuthManager\Models\OAuthService;

interface FileExplorerInterface
{
    /**
     * Initialize the explorer with an OAuth service
     */
    public function __construct(?OAuthService $oauthService = null);

    /**
     * Set the OAuth service for this explorer
     */
    public function setOAuthService(OAuthService $oauthService): self;

    /**
     * Get the current OAuth service
     */
    public function getOAuthService(): ?OAuthService;

    /**
     * List files and folders in a specific folder
     * 
     * @param string $folderId The folder ID to list files from
     * @param int $pageSize Number of files to return per page
     * @return array Array containing 'files' and optionally 'nextPageToken'
     */
    public function listFiles(string $folderId = 'root', int $pageSize = 50): array;

    /**
     * Get file information by ID
     * 
     * @param string $fileId The file ID
     * @return array File information
     */
    public function getFile(string $fileId): array;

    /**
     * Search for files
     * 
     * @param string $query Search query
     * @param string $folderId Optional folder to search within
     * @return array Array of matching files
     */
    public function searchFiles(string $query, string $folderId = 'root'): array;

    /**
     * Get breadcrumb navigation for a folder
     * 
     * @param string $folderId The folder ID
     * @return array Array of breadcrumb items
     */
    public function getBreadcrumb(string $folderId): array;

    /**
     * Test the connection to the service
     * 
     * @return bool True if connection is successful
     */
    public function testConnection(): bool;

    /**
     * Get the service type identifier (e.g., 'google_drive', 'dropbox', 'onedrive')
     * 
     * @return string Service type identifier
     */
    public function getServiceType(): string;

    /**
     * Get supported file types/mime types for this explorer
     * 
     * @return array Array of supported mime types
     */
    public function getSupportedMimeTypes(): array;

    /**
     * Check if a file type is supported by this explorer
     * 
     * @param string $mimeType The mime type to check
     * @return bool True if supported
     */
    public function supportsMimeType(string $mimeType): bool;


    /**
     * Download a file by its ID
     * 
     * @param string $fileId The file ID
     * @return mixed Raw file content or service-specific response
     */
    public function downloadFile(string $fileId);
}
