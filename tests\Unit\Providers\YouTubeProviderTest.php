<?php

namespace LBCDev\OAuthManager\Tests\Unit\Providers;

use LBCDev\OAuthManager\Tests\TestCase;
use LBCDev\OAuthManager\Models\OAuthService;
use LBCDev\OAuthManager\Providers\YouTubeProvider;
use League\OAuth2\Client\Provider\Google;
use League\OAuth2\Client\Token\AccessToken;
use Mockery;

class YouTubeProviderTest extends TestCase
{
    private YouTubeProvider $provider;
    private OAuthService $service;

    protected function setUp(): void
    {
        parent::setUp();

        $this->service = new OAuthService([
            'service_type' => 'youtube',
            'name' => 'Test YouTube',
            'slug' => 'test-youtube',
            'credentials' => [
                'client_id' => 'test_client_id',
                'client_secret' => 'test_client_secret',
            ],
            'access_token' => 'test_access_token',
            'refresh_token' => 'test_refresh_token',
        ]);

        $this->provider = new YouTubeProvider($this->service);
    }

    public function test_get_authorization_url_returns_valid_url()
    {
        $url = $this->provider->getAuthorizationUrl();

        $this->assertStringContainsString('https://accounts.google.com/o/oauth2/', $url);
        $this->assertStringContainsString('client_id=test_client_id', $url);
        $this->assertStringContainsString('access_type=offline', $url);
    }

    public function test_handle_callback_success()
    {
        $mockProvider = Mockery::mock(Google::class);
        $mockToken = Mockery::mock(AccessToken::class);

        $mockToken->shouldReceive('getToken')->andReturn('new_access_token');
        $mockToken->shouldReceive('getRefreshToken')->andReturn('new_refresh_token');
        $mockToken->shouldReceive('getExpires')->andReturn(time() + 3600);

        $mockProvider->shouldReceive('getAccessToken')
            ->with('authorization_code', ['code' => 'test_code'])
            ->andReturn($mockToken);

        $fakeProvider = new FakeYouTubeProvider($this->service);
        $fakeProvider->setMockProvider($mockProvider);

        $result = $fakeProvider->handleCallback('test_code');

        $this->assertArrayHasKey('access_token', $result);
        $this->assertArrayHasKey('refresh_token', $result);
        $this->assertArrayHasKey('expires_at', $result);
    }

    public function test_refresh_token_success()
    {
        $mockProvider = Mockery::mock(Google::class);
        $mockToken = Mockery::mock(AccessToken::class);

        $mockToken->shouldReceive('getToken')->andReturn('refreshed_access_token');
        $mockToken->shouldReceive('getRefreshToken')->andReturn('refreshed_refresh_token');
        $mockToken->shouldReceive('getExpires')->andReturn(time() + 3600);

        $mockProvider->shouldReceive('getAccessToken')
            ->with('refresh_token', ['refresh_token' => 'test_refresh_token'])
            ->andReturn($mockToken);

        $provider = new FakeYouTubeProvider($this->service);
        $provider->setMockProvider($mockProvider);

        $result = $provider->refreshToken();

        $this->assertIsArray($result);
        $this->assertEquals('refreshed_access_token', $result['access_token']);
        $this->assertEquals('refreshed_refresh_token', $result['refresh_token']);
    }

    public function test_refresh_token_failure()
    {
        $mockProvider = Mockery::mock(Google::class);
        $mockProvider->shouldReceive('getAccessToken')
            ->andThrow(new \Exception('Token refresh failed'));

        $provider = new FakeYouTubeProvider($this->service);
        $provider->setMockProvider($mockProvider);

        $result = $provider->refreshToken();

        $this->assertNull($result);
    }

    // public function test_test_connection_success()
    // {
    //     $mockClient = Mockery::mock(\Google_Client::class);
    //     $mockYouTubeService = Mockery::mock(\Google\Service\YouTube::class);
    //     $mockChannels = Mockery::mock(\Google\Service\YouTube\Resource\Channels::class);
    //     $mockChannelListResponse = Mockery::mock(\Google\Service\YouTube\ChannelListResponse::class);
    //     $mockChannel = Mockery::mock(\Google\Service\YouTube\Channel::class);

    //     $mockChannelListResponse->shouldReceive('getItems')->andReturn([$mockChannel]);
    //     $mockChannels->shouldReceive('listChannels')->with('snippet', ['mine' => true])->andReturn($mockChannelListResponse);
    //     $mockYouTubeService->channels = $mockChannels;

    //     // Expect setAccessToken to be called
    //     $mockClient->shouldReceive('setAccessToken')->once()->with('test_access_token');

    //     $fakeProvider = new FakeYouTubeProvider($this->service);
    //     $fakeProvider->setMockClient($mockClient);
    //     $fakeProvider->setMockYouTubeService($mockYouTubeService);

    //     $result = $fakeProvider->testConnection();

    //     $this->assertTrue($result);
    // }

    // public function test_test_connection_failure()
    // {
    //     $mockClient = Mockery::mock(\Google_Client::class);
    //     $mockClient->shouldReceive('setAccessToken')->andThrow(new \Exception('Connection failed'));

    //     $fakeProvider = new FakeYouTubeProvider($this->service);
    //     $fakeProvider->setMockClient($mockClient);

    //     $result = $fakeProvider->testConnection();

    //     $this->assertFalse($result);
    // }

    // public function test_revoke_token_success()
    // {
    //     $mockClient = Mockery::mock(\Google_Client::class);
    //     $mockClient->shouldReceive('setAccessToken')->once()->with('test_access_token');
    //     $mockClient->shouldReceive('revokeToken')->once()->andReturn(true);

    //     $fakeProvider = new FakeYouTubeProvider($this->service);
    //     $fakeProvider->setMockClient($mockClient);

    //     $result = $fakeProvider->revokeToken();

    //     $this->assertTrue($result);
    // }

    // public function test_revoke_token_failure()
    // {
    //     $mockClient = Mockery::mock(\Google_Client::class);
    //     $mockClient->shouldReceive('setAccessToken')->andThrow(new \Exception('Revoke failed'));

    //     $fakeProvider = new FakeYouTubeProvider($this->service);
    //     $fakeProvider->setMockClient($mockClient);

    //     $result = $fakeProvider->revokeToken();

    //     $this->assertFalse($result);
    // }

    // public function test_get_channel_info_success()
    // {
    //     $mockClient = Mockery::mock(\Google_Client::class);
    //     $mockYouTubeService = Mockery::mock(\Google\Service\YouTube::class);
    //     $mockChannels = Mockery::mock(\Google\Service\YouTube\Resource\Channels::class);
    //     $mockChannelListResponse = Mockery::mock(\Google\Service\YouTube\ChannelListResponse::class);
    //     $mockChannel = Mockery::mock(\Google\Service\YouTube\Channel::class);
    //     $mockSnippet = Mockery::mock(\Google\Service\YouTube\ChannelSnippet::class);
    //     $mockStatistics = Mockery::mock(\Google\Service\YouTube\ChannelStatistics::class);
    //     $mockThumbnails = Mockery::mock(\Google\Service\YouTube\ThumbnailDetails::class);
    //     $mockThumbnail = Mockery::mock(\Google\Service\YouTube\Thumbnail::class);

    //     // Setup mock responses
    //     $mockThumbnail->shouldReceive('getUrl')->andReturn('https://example.com/thumbnail.jpg');
    //     $mockThumbnails->shouldReceive('getDefault')->andReturn($mockThumbnail);

    //     $mockSnippet->shouldReceive('getTitle')->andReturn('Test Channel');
    //     $mockSnippet->shouldReceive('getDescription')->andReturn('Test Description');
    //     $mockSnippet->shouldReceive('getThumbnails')->andReturn($mockThumbnails);
    //     $mockSnippet->shouldReceive('getPublishedAt')->andReturn('2023-01-01T00:00:00Z');

    //     $mockStatistics->shouldReceive('getSubscriberCount')->andReturn('1000');
    //     $mockStatistics->shouldReceive('getVideoCount')->andReturn('50');
    //     $mockStatistics->shouldReceive('getViewCount')->andReturn('10000');

    //     $mockChannel->shouldReceive('getId')->andReturn('test_channel_id');
    //     $mockChannel->shouldReceive('getSnippet')->andReturn($mockSnippet);
    //     $mockChannel->shouldReceive('getStatistics')->andReturn($mockStatistics);

    //     $mockChannelListResponse->shouldReceive('getItems')->andReturn([$mockChannel]);
    //     $mockChannels->shouldReceive('listChannels')->with('snippet,statistics', ['mine' => true])->andReturn($mockChannelListResponse);
    //     $mockYouTubeService->channels = $mockChannels;

    //     $mockClient->shouldReceive('setAccessToken')->once()->with('test_access_token');

    //     $fakeProvider = new FakeYouTubeProvider($this->service);
    //     $fakeProvider->setMockClient($mockClient);
    //     $fakeProvider->setMockYouTubeService($mockYouTubeService);

    //     $result = $fakeProvider->getChannelInfo();

    //     $this->assertIsArray($result);
    //     $this->assertEquals('test_channel_id', $result['id']);
    //     $this->assertEquals('Test Channel', $result['title']);
    //     $this->assertEquals('Test Description', $result['description']);
    //     $this->assertEquals('1000', $result['subscriber_count']);
    // }

    protected function tearDown(): void
    {
        Mockery::close();
        parent::tearDown();
    }
}

class FakeYouTubeProvider extends YouTubeProvider
{
    protected Google $mockProvider;
    protected $mockClient;
    protected $mockYouTubeService;

    public function setMockProvider(Google $provider)
    {
        $this->mockProvider = $provider;
    }

    public function setMockClient($client)
    {
        $this->mockClient = $client;
    }

    public function setMockYouTubeService($youtubeService)
    {
        $this->mockYouTubeService = $youtubeService;
    }

    protected function getProvider(): Google
    {
        return $this->mockProvider ?? parent::getProvider();
    }

    // public function getClient(): \Google_Client
    // {
    //     if ($this->mockClient) {
    //         $this->mockClient->setAccessToken($this->service->access_token);
    //         return $this->mockClient;
    //     }
    //     return parent::getClient();
    // }

    // protected function createYouTubeService(\Google_Client $client): \Google\Service\YouTube
    // {
    //     return $this->mockYouTubeService ?? parent::createYouTubeService($client);
    // }
}
