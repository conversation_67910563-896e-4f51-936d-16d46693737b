<?php

namespace LBCDev\OAuthManager\Tests\Feature\Commands;

use Illuminate\Foundation\Testing\RefreshDatabase;
use LBCDev\OAuthManager\Models\OAuthService;
use LBCDev\OAuthManager\Tests\TestCase;
use Symfony\Component\Console\Output\BufferedOutput;

class ShowCommandTest extends TestCase
{
    use RefreshDatabase;

    public function test_show_all_services_when_no_services_exist()
    {
        $output = new BufferedOutput();

        $this->app->make(\Illuminate\Contracts\Console\Kernel::class)->call('oauth:show', [], $output);

        $display = $output->fetch();

        $this->assertStringContainsString('⚠️  No hay servicios OAuth configurados', $display);
    }

    public function test_show_all_services_displays_multiple_services()
    {
        // Crear servicios de prueba
        $service1 = OAuthService::factory()->create([
            'name' => 'Google Drive Principal',
            'slug' => 'google-drive-principal',
            'service_type' => 'google_drive',
            'is_active' => true,
            'access_token' => 'valid_access_token_123',
            'refresh_token' => 'valid_refresh_token_456',
            'expires_at' => now()->addHour(),
        ]);

        $service2 = OAuthService::factory()->create([
            'name' => 'Dropbox Secundario',
            'slug' => 'dropbox-secundario',
            'service_type' => 'dropbox',
            'is_active' => false,
            'access_token' => null,
            'refresh_token' => null,
            'expires_at' => null,
        ]);

        $output = new BufferedOutput();

        $this->app->make(\Illuminate\Contracts\Console\Kernel::class)->call('oauth:show', [], $output);

        $display = $output->fetch();

        // Verificar que se muestran ambos servicios
        $this->assertStringContainsString('📋 Lista de servicios OAuth (2 servicios)', $display);
        $this->assertStringContainsString('Google Drive Principal', $display);
        $this->assertStringContainsString('Dropbox Secundario', $display);
        $this->assertStringContainsString('google-drive-principal', $display);
        $this->assertStringContainsString('dropbox-secundario', $display);
    }

    public function test_show_single_service_by_slug()
    {
        $service = OAuthService::factory()->create([
            'name' => 'Mi Google Drive',
            'slug' => 'mi-google-drive',
            'service_type' => 'google_drive',
            'is_active' => true,
            'access_token' => 'access_token_example_123456789',
            'refresh_token' => 'refresh_token_example_987654321',
            'expires_at' => now()->addHour(),
        ]);

        $output = new BufferedOutput();

        $this->app->make(\Illuminate\Contracts\Console\Kernel::class)->call('oauth:show', [
            'slug' => 'mi-google-drive'
        ], $output);

        $display = $output->fetch();

        // Verificar información del servicio
        $this->assertStringContainsString('📋 Información del servicio OAuth', $display);
        $this->assertStringContainsString('Mi Google Drive', $display);
        $this->assertStringContainsString('mi-google-drive', $display);
        $this->assertStringContainsString('google_drive', $display);
        $this->assertStringContainsString('✅ Activo', $display);
        $this->assertStringContainsString('✅ Válido', $display);

        // Verificar que los tokens se muestran truncados
        $this->assertStringContainsString('access_tok..._123456789', $display);
        $this->assertStringContainsString('refresh_to..._987654321', $display);
    }

    public function test_show_single_service_not_found()
    {
        $output = new BufferedOutput();

        $this->app->make(\Illuminate\Contracts\Console\Kernel::class)->call('oauth:show', [
            'slug' => 'servicio-inexistente'
        ], $output);

        $display = $output->fetch();

        $this->assertStringContainsString("❌ Servicio con slug 'servicio-inexistente' no encontrado", $display);
    }

    public function test_show_service_with_expired_token_and_refresh_token()
    {
        $service = OAuthService::factory()->create([
            'name' => 'Servicio Expirado',
            'slug' => 'servicio-expirado',
            'service_type' => 'google_drive',
            'is_active' => true,
            'access_token' => 'expired_token',
            'refresh_token' => 'valid_refresh_token',
            'expires_at' => now()->subHour(), // Expirado hace una hora
        ]);

        $output = new BufferedOutput();

        $this->app->make(\Illuminate\Contracts\Console\Kernel::class)->call('oauth:show', [
            'slug' => 'servicio-expirado'
        ], $output);

        $display = $output->fetch();

        $this->assertStringContainsString('⏰ Expirado (puede refrescarse)', $display);
    }

    public function test_show_service_with_expired_token_without_refresh_token()
    {
        $service = OAuthService::factory()->create([
            'name' => 'Servicio Sin Refresh',
            'slug' => 'servicio-sin-refresh',
            'service_type' => 'google_drive',
            'is_active' => true,
            'access_token' => 'expired_token',
            'refresh_token' => null,
            'expires_at' => now()->subHour(),
        ]);

        $output = new BufferedOutput();

        $this->app->make(\Illuminate\Contracts\Console\Kernel::class)->call('oauth:show', [
            'slug' => 'servicio-sin-refresh'
        ], $output);

        $display = $output->fetch();

        $this->assertStringContainsString('⏰ Expirado (sin refresh token)', $display);
    }

    public function test_show_service_without_authorization()
    {
        $service = OAuthService::factory()->create([
            'name' => 'Servicio No Autorizado',
            'slug' => 'servicio-no-autorizado',
            'service_type' => 'google_drive',
            'is_active' => true,
            'access_token' => null,
            'refresh_token' => null,
            'expires_at' => null,
        ]);

        $output = new BufferedOutput();

        $this->app->make(\Illuminate\Contracts\Console\Kernel::class)->call('oauth:show', [
            'slug' => 'servicio-no-autorizado'
        ], $output);

        $display = $output->fetch();

        $this->assertStringContainsString('❌ No autorizado', $display);
        $this->assertStringContainsString('No disponible', $display);
    }

    public function test_show_inactive_service()
    {
        $service = OAuthService::factory()->create([
            'name' => 'Servicio Inactivo',
            'slug' => 'servicio-inactivo',
            'service_type' => 'dropbox',
            'is_active' => false,
            'access_token' => 'some_token',
            'refresh_token' => 'some_refresh_token',
            'expires_at' => now()->addHour(),
        ]);

        $output = new BufferedOutput();

        $this->app->make(\Illuminate\Contracts\Console\Kernel::class)->call('oauth:show', [
            'slug' => 'servicio-inactivo'
        ], $output);

        $display = $output->fetch();

        $this->assertStringContainsString('❌ Inactivo', $display);
    }

    public function test_token_truncation_works_correctly()
    {
        $service = OAuthService::factory()->create([
            'name' => 'Test Token Truncation',
            'slug' => 'test-token-truncation',
            'service_type' => 'google_drive',
            'access_token' => 'short_token',
            'refresh_token' => 'this_is_a_very_long_token_that_should_be_truncated_properly_123456789',
        ]);

        $output = new BufferedOutput();

        $this->app->make(\Illuminate\Contracts\Console\Kernel::class)->call('oauth:show', [
            'slug' => 'test-token-truncation'
        ], $output);

        $display = $output->fetch();

        // Token corto no debe truncarse
        $this->assertStringContainsString('short_token', $display);

        // Token largo debe truncarse
        $this->assertStringContainsString('this_is_a_..._123456789', $display);
        $this->assertStringNotContainsString('this_is_a_very_long_token_that_should_be_truncated_properly_123456789', $display);
    }

    public function test_full_tokens_option_shows_complete_tokens()
    {
        $service = OAuthService::factory()->create([
            'name' => 'Test Full Tokens',
            'slug' => 'test-full-tokens',
            'service_type' => 'google_drive',
            'access_token' => 'this_is_a_very_long_access_token_that_should_be_shown_completely_123456789',
            'refresh_token' => 'this_is_a_very_long_refresh_token_that_should_be_shown_completely_987654321',
        ]);

        $output = new BufferedOutput();

        $this->app->make(\Illuminate\Contracts\Console\Kernel::class)->call('oauth:show', [
            'slug' => 'test-full-tokens',
            '--full-tokens' => true
        ], $output);

        $display = $output->fetch();

        // Los tokens completos deben mostrarse
        $this->assertStringContainsString('this_is_a_very_long_access_token_that_should_be_shown_completely_123456789', $display);
        $this->assertStringContainsString('this_is_a_very_long_refresh_token_that_should_be_shown_completely_987654321', $display);

        // No debe haber truncado
        $this->assertStringNotContainsString('this_is_a_...456789', $display);
        $this->assertStringNotContainsString('this_is_a_...654321', $display);
    }

    public function test_full_tokens_option_works_with_all_services()
    {
        $service1 = OAuthService::factory()->create([
            'name' => 'Service 1',
            'slug' => 'service-1',
            'service_type' => 'google_drive',
            'access_token' => 'very_long_access_token_for_service_one_123456789',
        ]);

        $service2 = OAuthService::factory()->create([
            'name' => 'Service 2',
            'slug' => 'service-2',
            'service_type' => 'dropbox',
            'access_token' => 'very_long_access_token_for_service_two_987654321',
        ]);

        $output = new BufferedOutput();

        $this->app->make(\Illuminate\Contracts\Console\Kernel::class)->call('oauth:show', [
            '--full-tokens' => true
        ], $output);

        $display = $output->fetch();

        // Ambos tokens completos deben mostrarse
        $this->assertStringContainsString('very_long_access_token_for_service_one_123456789', $display);
        $this->assertStringContainsString('very_long_access_token_for_service_two_987654321', $display);
    }
}
