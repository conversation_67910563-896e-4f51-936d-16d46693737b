<?php

namespace <PERSON>BC<PERSON>ev\OAuthManager\Tests\Unit;

use Illuminate\Support\Facades\Route;
use Illuminate\Support\Facades\Artisan;
use LBCDev\OAuthManager\Tests\TestCase;
use LBCDev\OAuthManager\Services\OAuthManager;
use LBCDev\OAuthManager\Console\Commands\RefreshTokenCommand;
use LBCDev\OAuthManager\Console\Commands\TestOAuthConnectionCommand;

class OAuthManagerServiceProviderTest extends TestCase
{
    public function test_commands_are_registered()
    {
        $kernel = app(\Illuminate\Contracts\Console\Kernel::class);

        $commands = Artisan::all();

        $this->assertArrayHasKey('oauth:test', $commands);
        $this->assertArrayHasKey('oauth:refresh', $commands);
        $this->assertArrayHasKey('oauth:show', $commands);
    }

    public function test_config_is_merged()
    {
        $this->assertArrayHas<PERSON>ey('oauth-manager', config());
        $this->assertArray<PERSON><PERSON><PERSON><PERSON>('services', config('oauth-manager'));
    }

    public function test_routes_are_loaded()
    {
        $this->assertTrue(Route::has('oauth-manager.authorize'));
        $this->assertTrue(Route::has('oauth-manager.callback'));
    }
}
