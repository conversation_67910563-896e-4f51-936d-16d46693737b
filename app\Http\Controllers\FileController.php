<?php

namespace App\Http\Controllers;

use App\Models\File;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Response;
use LBCDev\OAuthFileExplorer\Services\FileExplorerFactory;

class FileController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        $files = File::all();

        return view('files.index', compact('files'));
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        //
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        //
    }

    /**
     * Display the specified resource.
     */
    public function show(File $file)
    {
        //
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(File $file)
    {
        //
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, File $file)
    {
        //
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(File $file)
    {
        //
    }

    public function download(File $file)
    {
        $oauth = $file->oauthService;

        // Validar que existe servicio vinculado
        if (!$oauth) {
            abort(404, 'Servicio OAuth no vinculado al archivo');
        }

        // Verificar y refrescar token si es necesario
        if (!$oauth->ensureValidToken()) {
            abort(403, 'Token inválido. El administrador debe volver a autorizar el servicio.');
        }

        if (!$file->remote_file_id) {
            abort(400, 'No se pudo determinar el ID de archivo remoto');
        }

        try {
            $explorer = FileExplorerFactory::create($oauth);

            $rawFile = $explorer->downloadFile($file->remote_file_id);

            return Response::make($rawFile, 200, [
                'Content-Type' => $file->mime_type,
                'Content-Disposition' => 'inline; filename="' . $file->nombre . '"'
            ]);
        } catch (\Exception $e) {
            return response("Error al descargar el archivo: " . $e->getMessage(), 500);
        }
    }
}
