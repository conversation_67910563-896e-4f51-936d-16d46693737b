<?php

namespace App\Models;

use App\Models\Language;
use Illuminate\Database\Eloquent\Model;
use LBCDev\OAuthManager\Models\OAuthService;

class File extends Model
{
    protected $fillable = [
        'nombre',
        'url',
        'metadata',
        'oauth_service_id',
        'language_id',
        'orden',
    ];

    protected $casts = [
        'metadata' => 'array',
    ];

    public function products()
    {
        return $this->belongsToMany(Product::class);
    }

    public function language()
    {
        return $this->belongsTo(Language::class);
    }

    public function oauthService()
    {
        return $this->belongsTo(OAuthService::class);
    }

    public function getRemoteFileIdAttribute(): ?string
    {
        $oauthService = $this->oauthService;

        if (!$oauthService) {
            return null;
        }

        return match ($oauthService->service_type) {
            'google_drive' => $this->metadata['id'] ?? null,
            'dropbox' => $this->metadata['id'] ?? null,
            'onedrive' => $this->metadata['id'] ?? null,
            // 'youtube' => $this->youtube_id,
            // 'mega' => $this->mega_id,
            default => null
        };
    }

    protected function getGoogleDriveIdAttribute(): ?string
    {
        if (!$this->url) {
            return null;
        }

        // check if the url is a google drive url
        if (!str_contains($this->url, 'https://drive.google.com')) {
            return null;
        }

        // Captura el ID en diferentes formatos comunes de Drive
        if (preg_match('/\/d\/([a-zA-Z0-9_-]+)/', $this->url, $matches)) {
            return $matches[1];
        }

        if (preg_match('/id=([a-zA-Z0-9_-]+)/', $this->url, $matches)) {
            return $matches[1];
        }

        if (preg_match('/https:\/\/drive\.google\.com\/uc\?export=download&id=([a-zA-Z0-9_-]+)/', $this->url, $matches)) {
            return $matches[1];
        }

        return null;
    }

    protected function getDropboxIdAttribute(): ?string
    {
        if (!$this->url) {
            return null;
        }

        // check if the url is a dropbox url
        if (!str_contains($this->url, 'https://www.dropbox.com')) {
            return null;
        }

        // Captura el ID en diferentes formatos comunes de Dropbox
        if (preg_match('/\/s\/([a-zA-Z0-9_-]+)/', $this->url, $matches)) {
            return $matches[1];
        }

        if (preg_match('/\/dl\/([a-zA-Z0-9_-]+)/', $this->url, $matches)) {
            return $matches[1];
        }

        return null;
    }
}
