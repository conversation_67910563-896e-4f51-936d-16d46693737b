<?php

namespace LBCDev\OAuthFileExplorer\Tests\Unit;

use LBC<PERSON>ev\OAuthFileExplorer\Tests\TestCase;
use LBCDev\OAuthFileExplorer\Services\AbstractFileExplorer;
use LBCDev\OAuthManager\Models\OAuthService;
use PHPUnit\Framework\Attributes\Test;

class AbstractFileExplorerTest extends TestCase
{
    public function test_it_can_set_and_get_oauth_service()
    {
        $explorer = $this->createMockExplorer();

        $oauthService = OAuthService::create([
            'name' => 'Test Service',
            'service_type' => 'test',
            'access_token' => 'test_token',
            'is_active' => true,
        ]);

        $explorer->setOAuthService($oauthService);

        $this->assertEquals($oauthService->id, $explorer->getOAuthService()->id);
    }

    public function test_it_supports_all_mime_types_by_default()
    {
        $explorer = $this->createMockExplorer();

        $this->assertTrue($explorer->supportsMimeType('application/pdf'));
        $this->assertTrue($explorer->supportsMimeType('image/jpeg'));
        $this->assertTrue($explorer->supportsMimeType('video/mp4'));
    }

    public function test_it_can_check_specific_mime_types_when_defined()
    {
        $explorer = $this->createMockExplorerWithMimeTypes(['application/pdf', 'image/jpeg']);

        $this->assertTrue($explorer->supportsMimeType('application/pdf'));
        $this->assertTrue($explorer->supportsMimeType('image/jpeg'));
        $this->assertFalse($explorer->supportsMimeType('video/mp4'));
    }

    public function test_it_creates_standard_file_structure()
    {
        $explorer = $this->createMockExplorer();

        $reflection = new \ReflectionClass($explorer);
        $method = $reflection->getMethod('getStandardFileStructure');
        $method->setAccessible(true);

        $result = $method->invoke($explorer, 'file123', 'test.pdf', 'application/pdf', false, [
            'size' => 1024,
            'modifiedTime' => '2023-01-01T12:00:00Z'
        ]);

        $this->assertEquals('file123', $result['id']);
        $this->assertEquals('test.pdf', $result['name']);
        $this->assertEquals('application/pdf', $result['mimeType']);
        $this->assertFalse($result['isFolder']);
        $this->assertEquals(1024, $result['size']);
        $this->assertEquals('2023-01-01T12:00:00Z', $result['modifiedTime']);
        $this->assertEquals('test', $result['serviceType']);
    }

    public function test_it_identifies_folders_correctly()
    {
        $explorer = $this->createMockExplorer();

        $reflection = new \ReflectionClass($explorer);
        $method = $reflection->getMethod('isFolder');
        $method->setAccessible(true);

        $this->assertTrue($method->invoke($explorer, 'application/vnd.google-apps.folder'));
        $this->assertFalse($method->invoke($explorer, 'application/pdf'));
    }

    public function test_it_throws_exception_when_oauth_service_not_configured()
    {
        $explorer = $this->createMockExplorer();

        $result = $explorer->testConnection();

        $this->assertFalse($result);
    }

    public function test_it_throws_exception_when_oauth_service_is_inactive()
    {
        $oauthService = OAuthService::create([
            'name' => 'Inactive Service',
            'service_type' => 'test',
            'access_token' => 'test_token',
            'is_active' => false,
        ]);

        $explorer = $this->createMockExplorer($oauthService);

        $result = $explorer->testConnection();

        $this->assertFalse($result);
    }

    public function test_it_throws_exception_when_no_access_token()
    {
        $oauthService = OAuthService::create([
            'name' => 'No Token Service',
            'service_type' => 'test',
            'access_token' => null,
            'is_active' => true,
        ]);

        $explorer = $this->createMockExplorer($oauthService);

        $result = $explorer->testConnection();

        $this->assertFalse($result);
    }

    private function createMockExplorer(?OAuthService $oauthService = null)
    {
        return new class($oauthService) extends AbstractFileExplorer {
            public function getServiceType(): string
            {
                return 'test';
            }

            protected function initializeService(): void
            {
                // Mock implementation
            }

            protected function formatFileData($file): array
            {
                return [];
            }

            protected function performConnectionTest(): bool
            {
                return true;
            }

            public function listFiles(string $folderId = 'root', int $pageSize = 50): array
            {
                return ['files' => []];
            }

            public function getFile(string $fileId): array
            {
                return [];
            }

            public function searchFiles(string $query, string $folderId = 'root'): array
            {
                return [];
            }

            public function getBreadcrumb(string $folderId): array
            {
                return [];
            }
        };
    }

    private function createMockExplorerWithMimeTypes(array $mimeTypes)
    {
        $mock = new class(null) extends AbstractFileExplorer {
            private array $supportedMimeTypes = [];

            public function setSupportedMimeTypes(array $mimeTypes): void
            {
                $this->supportedMimeTypes = $mimeTypes;
            }

            public function getServiceType(): string
            {
                return 'test';
            }

            public function getSupportedMimeTypes(): array
            {
                return $this->supportedMimeTypes;
            }

            protected function initializeService(): void
            {
                // Mock implementation
            }

            protected function formatFileData($file): array
            {
                return [];
            }

            protected function performConnectionTest(): bool
            {
                return true;
            }

            public function listFiles(string $folderId = 'root', int $pageSize = 50): array
            {
                return ['files' => []];
            }

            public function getFile(string $fileId): array
            {
                return [];
            }

            public function searchFiles(string $query, string $folderId = 'root'): array
            {
                return [];
            }

            public function getBreadcrumb(string $folderId): array
            {
                return [];
            }
        };

        $mock->setSupportedMimeTypes($mimeTypes);
        return $mock;
    }
}
